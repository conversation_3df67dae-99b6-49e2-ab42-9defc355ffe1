// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

module.exports = {
  getTestsByLoincNums: (loincNums) => {
    const query = `SELECT * FROM c WHERE c.LOINC_NUM IN (${loincNums
      .map((num) => `"${num}"`)
      .join(', ')})`
    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] getTestsByLoincNums - Uses: IN with dynamic list')
    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] Query:', query)
    return query
  },
  getAllLabTestsQuery: () => {
    return 'SELECT c.LONG_COMMON_NAME,c.SHORTNAME,c.DisplayName,c.id FROM c WHERE c.isActive = true'
  },
  searchLabTestsQuery: (searchText, classFilter) => {
    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] searchLabTestsQuery - Uses: STARTSWITH, LOWER, TRIM, CONTAINS, IS_DEFINED, IN')
    const safeText = escapeForCosmos((searchText || '').toLowerCase())
    const hasSearch = safeText !== ''
    const hasClassFilter = Array.isArray(classFilter) && classFilter.length > 0
    const classFilterInQuery = hasClassFilter
      ? classFilter.map((c) => `'${escapeForCosmos(c)}'`).join(', ')
      : null

    let query = `
      SELECT * FROM c
      WHERE 1=1
    `
    if (hasSearch) {
      query += `
        AND (
          STARTSWITH(LOWER(TRIM(c.DisplayName)), '${safeText}') OR
          STARTSWITH(LOWER(TRIM(c.SHORTNAME)), '${safeText}') OR
          STARTSWITH(LOWER(TRIM(c.LONG_COMMON_NAME)), '${safeText}') OR
          CONTAINS(LOWER(TRIM(c.DisplayName)), '${safeText}') OR 
          CONTAINS(LOWER(TRIM(c.SHORTNAME)), '${safeText}') OR 
          CONTAINS(LOWER(TRIM(c.LONG_COMMON_NAME)), '${safeText}')
        )
           AND (
    IS_DEFINED(c.DisplayName) OR 
    IS_DEFINED(c.SHORTNAME) OR 
    IS_DEFINED(c.LONG_COMMON_NAME)
  )
      `
    }

    if (hasClassFilter) {
      query += `
        AND c.CLASS IN (${classFilterInQuery})
      `
    }

    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] Query:', query.replace(/\s+/g, ' ').trim())
    return query
  },

  getAllDistinctClassValuesQuery: () => {
    const query = 'SELECT DISTINCT c.CLASS FROM c WHERE IS_DEFINED(c.CLASS)'
    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] getAllDistinctClassValuesQuery - Uses: DISTINCT, IS_DEFINED')
    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] Query:', query)
    return query
  },
  getOrganizationTestIdsWithPricingQuery: (organizationId) => {
    return `SELECT c.testId, c.price, c.departments FROM c WHERE c.organizationId = "${organizationId}" AND c.isActive = true`
  },
  searchLabTestsByIdsQuery: (testIds, searchText, classFilter) => {
    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] searchLabTestsByIdsQuery - Uses: IN with large list, STARTSWITH, LOWER, TRIM, CONTAINS, IS_DEFINED')
    // For large test ID arrays, we need to handle this differently
    // This function now supports batch processing
    const safeText = escapeForCosmos((searchText || '').toLowerCase())
    const hasSearch = safeText !== ''
    const hasClassFilter = Array.isArray(classFilter) && classFilter.length > 0
    const classFilterInQuery = hasClassFilter
      ? classFilter.map((c) => `'${escapeForCosmos(c)}'`).join(', ')
      : null

    if (testIds.length > 1000) {
      // Return a special marker to indicate batch processing is needed
      return {
        requiresBatchProcessing: true,
        searchText: safeText,
        hasSearch,
        classFilter: classFilterInQuery,
        hasClassFilter,
      }
    }

    const idsString = testIds.map((id) => `"${id}"`).join(', ')

    let query = `
      SELECT * FROM c
      WHERE c.id IN (${idsString})
    `

    if (hasSearch) {
      query += `
        AND (
          STARTSWITH(LOWER(TRIM(c.DisplayName)), '${safeText}') OR
          STARTSWITH(LOWER(TRIM(c.SHORTNAME)), '${safeText}') OR
          STARTSWITH(LOWER(TRIM(c.LONG_COMMON_NAME)), '${safeText}') OR
          CONTAINS(LOWER(TRIM(c.DisplayName)), '${safeText}') OR
          CONTAINS(LOWER(TRIM(c.SHORTNAME)), '${safeText}') OR
          CONTAINS(LOWER(TRIM(c.LONG_COMMON_NAME)), '${safeText}')
        )
        AND (
          IS_DEFINED(c.DisplayName) OR
          IS_DEFINED(c.SHORTNAME) OR
          IS_DEFINED(c.LONG_COMMON_NAME)
        )
      `
    }

    if (hasClassFilter) {
      query += `
        AND c.CLASS IN (${classFilterInQuery})
      `
    }

    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] Query:', query.replace(/\s+/g, ' ').trim())
    return query
  },

  searchLabTestsOptimizedQuery: (searchText, classFilter) => {
    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] searchLabTestsOptimizedQuery - Uses: STARTSWITH, LOWER, TRIM, CONTAINS, IS_DEFINED, IN')
    const safeText = escapeForCosmos((searchText || '').toLowerCase())
    const hasSearch = safeText !== ''
    const hasClassFilter = Array.isArray(classFilter) && classFilter.length > 0
    const classFilterInQuery = hasClassFilter
      ? classFilter.map((c) => `'${escapeForCosmos(c)}'`).join(', ')
      : null

    let query = `
      SELECT * FROM c
      WHERE 1=1
    `

    if (hasSearch) {
      query += `
        AND (
          STARTSWITH(LOWER(TRIM(c.DisplayName)), '${safeText}') OR
          STARTSWITH(LOWER(TRIM(c.SHORTNAME)), '${safeText}') OR
          STARTSWITH(LOWER(TRIM(c.LONG_COMMON_NAME)), '${safeText}') OR
          CONTAINS(LOWER(TRIM(c.DisplayName)), '${safeText}') OR
          CONTAINS(LOWER(TRIM(c.SHORTNAME)), '${safeText}') OR
          CONTAINS(LOWER(TRIM(c.LONG_COMMON_NAME)), '${safeText}')
        )
        AND (
          IS_DEFINED(c.DisplayName) OR
          IS_DEFINED(c.SHORTNAME) OR
          IS_DEFINED(c.LONG_COMMON_NAME)
        )
      `
    }

    if (hasClassFilter) {
      query += `
        AND c.CLASS IN (${classFilterInQuery})
      `
    }

    console.log('[DYNAMODB_UNSUPPORTED][LAB_TESTS] Query:', query.replace(/\s+/g, ' ').trim())
    return query
  },
}
function escapeForCosmos(value) {
  return value.replace(/'/g, "''")
}
