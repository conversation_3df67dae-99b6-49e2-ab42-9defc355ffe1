// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

const getMedicalHistoryAddictionByPatientIdQuery = (patientId) => {
  return `SELECT * FROM c WHERE c.patientId = '${patientId}'`
}

const getMedicalHistoryAddictionByIdQuery = (id) => {
  return `SELECT * FROM c WHERE c.id = '${id}'`
}

const getMedicalHistoryAddictionByStatusQuery = (status) => {
  return `SELECT * FROM c WHERE c.status = '${status}'`
}

const getMedicalHistoryAddictionByPatientAndDateRangeQuery = (patientId, startDate, endDate) => {
  const startTimestamp = new Date(startDate).getTime() / 1000
  const endTimestamp = new Date(endDate).getTime() / 1000
  return `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c._ts >= ${startTimestamp} AND c._ts <= ${endTimestamp} ORDER BY c._ts DESC`
}

const getMedicalHistoryAddictionByDiagnosisQuery = (diseaseName) => {
  const query = `SELECT * FROM c WHERE ARRAY_CONTAINS(c.diagnosis, {"diseaseName": "${diseaseName}"}, true)`
  console.log('[DYNAMODB_UNSUPPORTED][MEDICAL_HISTORY_ADDICTION] getMedicalHistoryAddictionByDiagnosisQuery - Uses: ARRAY_CONTAINS with object matching')
  console.log('[DYNAMODB_UNSUPPORTED][MEDICAL_HISTORY_ADDICTION] Query:', query)
  return query
}

const getMedicalHistoryAddictionBySubstanceHistoryQuery = (substanceType, history) => {
  const query = `SELECT * FROM c WHERE c.${substanceType}.history = '${history}'`
  console.log(`[DYNAMODB_UNSUPPORTED][MEDICAL_HISTORY_ADDICTION] getMedicalHistoryAddictionBySubstanceHistoryQuery - Uses: Dynamic attribute path (c.${substanceType}.history)`)
  console.log('[DYNAMODB_UNSUPPORTED][MEDICAL_HISTORY_ADDICTION] Query:', query)
  return query
}

const getMedicalHistoryAddictionWithNicotineTestQuery = () => {
  const query = `SELECT * FROM c WHERE IS_DEFINED(c.nicotineDependenceTest)`
  console.log('[DYNAMODB_UNSUPPORTED][MEDICAL_HISTORY_ADDICTION] getMedicalHistoryAddictionWithNicotineTestQuery - Uses: IS_DEFINED')
  console.log('[DYNAMODB_UNSUPPORTED][MEDICAL_HISTORY_ADDICTION] Query:', query)
  return query
}

const getMedicalHistoryAddictionByCreatedByQuery = (createdBy) => {
  return `SELECT * FROM c WHERE c.createdBy = '${createdBy}' ORDER BY c.createdAt DESC`
}

const getMedicalHistoryAddictionByDateRangeQuery = (startDate, endDate) => {
  return `SELECT * FROM c WHERE c.createdAt >= '${startDate}' AND c.createdAt <= '${endDate}' ORDER BY c.createdAt DESC`
}

module.exports = {
  getMedicalHistoryAddictionByPatientIdQuery,
  getMedicalHistoryAddictionByIdQuery,
  getMedicalHistoryAddictionByStatusQuery,
  getMedicalHistoryAddictionByPatientAndDateRangeQuery,
  getMedicalHistoryAddictionByDiagnosisQuery,
  getMedicalHistoryAddictionBySubstanceHistoryQuery,
  getMedicalHistoryAddictionWithNicotineTestQuery,
  getMedicalHistoryAddictionByCreatedByQuery,
  getMedicalHistoryAddictionByDateRangeQuery
}