// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

module.exports = {
  getTotalPatientsQuery: (organizationId) => {
    const queryStr = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @organizationId'
    const params = [{ name: '@organizationId', value: organizationId }]
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] getTotalPatientsQuery - Uses: COUNT aggregation')
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Parameters:', JSON.stringify(params))
    return { query: queryStr, parameters: params }
  },

  getTodaysAppointmentsQuery: (organizationId, todayString) => {
    const queryStr = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @organizationId AND STARTSWITH(c.date, @todayString)'
    const params = [
      { name: '@organizationId', value: organizationId },
      { name: '@todayString', value: todayString },
    ]
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] getTodaysAppointmentsQuery - Uses: COUNT, STARTSWITH')
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Parameters:', JSON.stringify(params))
    return { query: queryStr, parameters: params }
  },

  getPatientQueueQuery: (organizationId, todayString) => {
    const queryStr = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @organizationId AND (STARTSWITH(c.status, "Booked") OR c.status = "Booked-Arrived" OR c.status = "Booked-Booked") AND STARTSWITH(c.date, @todayString)'
    const params = [
      { name: '@organizationId', value: organizationId },
      { name: '@todayString', value: todayString },
    ]
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] getPatientQueueQuery - Uses: COUNT, STARTSWITH, OR conditions')
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Parameters:', JSON.stringify(params))
    return { query: queryStr, parameters: params }
  },

  getAverageWaitingTimeQuery: (organizationId, todayString) => {
    const queryStr = 'SELECT c.patientArrivalTime, c.consultationStartTime, c.consultationEndTime FROM c WHERE c.organizationId = @organizationId AND STARTSWITH(c.date, @todayString) AND IS_DEFINED(c.consultationStartTime) AND IS_DEFINED(c.patientArrivalTime)'
    const params = [
      { name: '@organizationId', value: organizationId },
      { name: '@todayString', value: todayString },
    ]
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] getAverageWaitingTimeQuery - Uses: STARTSWITH, IS_DEFINED')
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Parameters:', JSON.stringify(params))
    return { query: queryStr, parameters: params }
  },

  getUpcomingAppointmentsQuery: (organizationId, dateFilter, doctorId) => {
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] getUpcomingAppointmentsQuery - Uses: IS_DEFINED, NOT ARRAY_CONTAINS, LOWER, STARTSWITH')
    let query = `
      SELECT
        c.id as queueId,
        c.appointmentId,
        c.patientId,
        c.doctorId,
        c.date,
        c.time,
        c.status,
        c.type
      FROM c
      WHERE c.organizationId = @organizationId
        AND IS_DEFINED(c.status)
        AND NOT ARRAY_CONTAINS(@excludedStatuses, LOWER(c.status))
    `

    // Default: restrict to today and future dates
    const parameters = [
      { name: '@organizationId', value: organizationId }
    ]
    parameters.push({ name: '@excludedStatuses', value: ['canceled', 'consultation-done'] })

    if (!dateFilter) {
      const today = new Date().toISOString().split('T')[0]
      query += ` AND c.date >= @today`
      parameters.push({ name: '@today', value: today })
    }

    if (dateFilter) {
      let specificDate = null
      if (typeof dateFilter === 'string') {
        specificDate = dateFilter.split('T')[0]
      } else if (dateFilter && typeof dateFilter === 'object') {
        if (dateFilter.specificDate) specificDate = dateFilter.specificDate
        else if (dateFilter.date) {
          const d = new Date(dateFilter.date)
          if (!isNaN(d.getTime())) specificDate = d.toISOString().split('T')[0]
        }
      }

      if (specificDate) {
        query += ` AND STARTSWITH(c.date, @specificDate)`
        parameters.push({ name: '@specificDate', value: specificDate })
      }
    }

    // Add doctor filtering
    if (doctorId) {
      query += ` AND c.doctorId = @doctorId`
      parameters.push({ name: '@doctorId', value: doctorId })
    }

    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Query:', query.replace(/\s+/g, ' ').trim())
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Parameters:', JSON.stringify(parameters))
    return {
      query,
      parameters
    }
  },

  getPatientsByIdsQuery: (patientIds = []) => {
    if (!patientIds || patientIds.length === 0) return null
    const queryStr = 'SELECT c.id, c.name FROM c WHERE ARRAY_CONTAINS(@ids, c.id)'
    const params = [{ name: '@ids', value: patientIds }]
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] getPatientsByIdsQuery - Uses: ARRAY_CONTAINS with parameterized array')
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Parameters:', JSON.stringify(params))
    return { query: queryStr, parameters: params }
  },

  getDoctorsByIdsBulkQuery: (doctorIds = []) => {
    if (!doctorIds || doctorIds.length === 0) return null
    const queryStr = 'SELECT c.id, c.general, c.name FROM c WHERE ARRAY_CONTAINS(@ids, c.id) OR ARRAY_CONTAINS(@ids, c.general.doctorID)'
    const params = [{ name: '@ids', value: doctorIds }]
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] getDoctorsByIdsBulkQuery - Uses: ARRAY_CONTAINS with parameterized array, OR with nested property access')
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][DASHBOARD] Parameters:', JSON.stringify(params))
    return { query: queryStr, parameters: params }
  },

}
