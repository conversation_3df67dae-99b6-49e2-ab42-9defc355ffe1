// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

const buildUserFilterConditions = (organizationId, search, role, isActive) => {
  const conditions = []

  if (organizationId) {
    conditions.push(`c.organizationId = '${organizationId}'`)
  }

  if (search) {
    // NOTE: CONTAINS is not supported in DynamoDB - will need to use GSI or OpenSearch
    conditions.push(
      `(CONTAINS(c.name, '${search}') OR CONTAINS(c.email, '${search}') OR CONTAINS(c.phone, '${search}'))`,
    )
  }

  if (role) {
    conditions.push(`c.userRole = '${role}'`)
  }

  if (isActive !== null) {
    conditions.push(`c.isActive = ${isActive}`)
  }

  return conditions
}

const getUsersByOrganizationQuery = (
  organizationId,
  search,
  role,
  isActive,
  sortBy,
  sortOrder,
  pageSize = null,
  page = null,
) => {
  const conditions = buildUserFilterConditions(organizationId, search, role, isActive)

  let query = 'SELECT * FROM c'
  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`
  }
  query += ` ORDER BY c.${sortBy} ${sortOrder.toUpperCase()}`

  if (pageSize && page) {
    const offset = (page - 1) * pageSize
    query += ` OFFSET ${offset} LIMIT ${pageSize}`
  }

  // Log unsupported features
  if (search) {
    console.log('[DYNAMODB_UNSUPPORTED][USERS] getUsersByOrganizationQuery - Uses: CONTAINS (text search)')
  }
  if (pageSize && page) {
    console.log('[DYNAMODB_UNSUPPORTED][USERS] getUsersByOrganizationQuery - Uses: OFFSET/LIMIT pagination (DynamoDB uses cursor-based pagination)')
  }
  if (search || (pageSize && page)) {
    console.log('[DYNAMODB_UNSUPPORTED][USERS] Query:', query)
  }

  return query
}

const getUsersCountQuery = (organizationId, search, role, isActive) => {
  const conditions = buildUserFilterConditions(organizationId, search, role, isActive)

  let query = 'SELECT VALUE COUNT(1) FROM c'
  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`
  }

  console.log('[DYNAMODB_UNSUPPORTED][USERS] getUsersCountQuery - Uses: COUNT aggregation, CONTAINS (if search provided)')
  console.log('[DYNAMODB_UNSUPPORTED][USERS] Query:', query)
  return query
}

const getUserByEmailIncludingInactiveQuery = (email) => {
  return `SELECT * FROM c WHERE c.email = '${email}'`
}

module.exports = {
  getUsersByOrganizationQuery,
  getUsersCountQuery,
  getUserByEmailIncludingInactiveQuery,
}
