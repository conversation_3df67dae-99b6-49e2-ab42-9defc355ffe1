// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

module.exports = {
  getAllMedicinesQuery: () => {
    return `SELECT * FROM c`
  },
  getOrganizationMedicinesQuery: (organizationId) => {
    return `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
  },
  getMedicinesByProductIdsQuery: (productIds) => {
    return `SELECT * FROM c WHERE c.productId IN (${productIds
      .map((id) => `"${id}"`)
      .join(', ')})`
  },
  getOrganizationMedicineIdsWithPricingQuery: (organizationId) => {
    return `SELECT c.medicineId, c.price FROM c WHERE c.organizationId = "${organizationId}" AND c.isActive = true`
  },
  searchMedicinesByIdsQuery: (medicineIds, searchText) => {
    const idsString = medicineIds.map((id) => `"${id}"`).join(', ')
    const normalizedSearchText = searchText.trim().replace(/\s+/g, ' ')
    const query = `SELECT * FROM c WHERE c.id IN (${idsString}) AND (CONTAINS(LOWER(c.saltComposition), LOWER('${normalizedSearchText}')) OR CONTAINS(LOWER(c.productName), LOWER('${normalizedSearchText}')))`
    console.log('[DYNAMODB_UNSUPPORTED][MEDICINES] searchMedicinesByIdsQuery - Uses: IN with large list, CONTAINS, LOWER')
    console.log('[DYNAMODB_UNSUPPORTED][MEDICINES] Query:', query)
    return query
  },
}
