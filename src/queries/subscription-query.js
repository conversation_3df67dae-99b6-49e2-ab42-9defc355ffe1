const { ORGANIZATION_PLAN_ID } = require('../models/subscription-model')

// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

// IST Helper Functions (Indian Standard Time = UTC + 5:30)
// For comparing dates, we use IST-adjusted current time

function getISTNow() {
  const now = new Date()
  const istOffset = 330 * 60 * 1000 // 5.5 hours in milliseconds
  return new Date(now.getTime() + istOffset)
}

function getISTStartOfDay() {
  const istNow = getISTNow()
  return new Date(Date.UTC(istNow.getUTCFullYear(), istNow.getUTCMonth(), istNow.getUTCDate(), 0, 0, 0, 0))
}

function getISTISOString() {
  return getISTStartOfDay().toISOString()
}

function getISTDateWithDays(daysToAdd) {
  const startOfDay = getISTStartOfDay()
  startOfDay.setUTCDate(startOfDay.getUTCDate() + daysToAdd)
  return startOfDay
}

module.exports = {
  // Subscription Plan Queries
  getActivePlansQuery: () => {
    return `SELECT * FROM c WHERE c.isActive = true AND c.id != '${ORGANIZATION_PLAN_ID}' ORDER BY c.created_on DESC`
  },

  searchPlansQuery: (searchParams) => {
    const conditions = [
      'c.isActive = true',
      `c.id != '${ORGANIZATION_PLAN_ID}'`,
    ]
    const parameters = []

    if (searchParams.planName) {
      conditions.push('CONTAINS(LOWER(c.planName), LOWER(@planName))')
      parameters.push({ name: '@planName', value: searchParams.planName })
    }

    if (searchParams.validity) {
      conditions.push('c.validity = @validity')
      parameters.push({ name: '@validity', value: searchParams.validity })
    }

    const queryStr = `SELECT * FROM c WHERE ${conditions.join(' AND ')} ORDER BY c.created_on DESC`
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] searchPlansQuery - Uses: CONTAINS, LOWER, parameterized queries')
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] Parameters:', JSON.stringify(parameters))
    return {
      query: queryStr,
      parameters: parameters,
    }
  },

  getPlansByValidityQuery: (validity) => {
    return `SELECT * FROM c WHERE c.isActive = true AND c.id != '${ORGANIZATION_PLAN_ID}' AND c.validity = '${validity}' ORDER BY c.created_on DESC`
  },

  getPlanFeaturesQuery: (planId) => {
    return `SELECT c.id, c.planName, c.features, c.addOnFeatures FROM c WHERE c.id = '${planId}'`
  },

  
  getActiveSubscriptionByOrganizationQuery: (organizationId) => {
    return `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.status IN ('active', 'free trial', 'pending') ORDER BY c.created_on DESC`
  },

  getSubscriptionsByStatusQuery: (status, organizationId = null) => {
    let query = `SELECT * FROM c WHERE c.status = '${status}'`
    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`
    }
    query += ' ORDER BY c.created_on DESC'
    return query
  },

  getSubscriptionAnalyticsQuery: (organizationId = null) => {
    const baseQuery = organizationId
      ? `WHERE c.organizationId = '${organizationId}'`
      : ''
    const query = `
      SELECT
        COUNT(1) as totalSubscriptions,
        SUM(CASE WHEN c.status = 'active' THEN 1 ELSE 0 END) as activeSubscriptions,
        SUM(CASE WHEN c.status = 'expired' THEN 1 ELSE 0 END) as expiredSubscriptions,
        SUM(CASE WHEN c.status = 'cancelled' THEN 1 ELSE 0 END) as cancelledSubscriptions,
        SUM(c.totalAmount) as totalRevenue
      FROM c
      ${baseQuery}
    `
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] getSubscriptionAnalyticsQuery - Uses: COUNT, SUM, CASE WHEN (aggregations not supported in DynamoDB)')
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] Query:', query.replace(/\s+/g, ' ').trim())
    return query
  },

  getUpcomingRenewalsQuery: (daysAhead, organizationId = null) => {
    const futureDate = getISTDateWithDays(daysAhead)
    const currentISTDate = getISTISOString()

    let query = `
      SELECT * FROM c
      WHERE c.status = 'active'
      AND c.autoRenew = true
      AND c.endDate <= '${futureDate.toISOString()}'
      AND c.endDate >= '${currentISTDate}'
    `

    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`
    }

    query += ' ORDER BY c.endDate ASC'
    return query
  },

  getSubscriptionsByDateRangeQuery: (
    startDate,
    endDate,
    organizationId = null,
  ) => {
    let query = `SELECT * FROM c WHERE c.created_on >= '${startDate}' AND c.created_on <= '${endDate}'`
    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`
    }
    query += ' ORDER BY c.created_on DESC'
    return query
  },

  getRevenueByPlanQuery: (startDate = null, endDate = null) => {
    let dateFilter = ''
    if (startDate && endDate) {
      dateFilter = `AND c.created_on >= '${startDate}' AND c.created_on <= '${endDate}'`
    }

    const query = `
      SELECT c.planName, c.planId,
             COUNT(1) as subscriptionCount,
             SUM(c.totalAmount) as totalRevenue
      FROM c
      WHERE c.status IN ('active', 'expired')
      ${dateFilter}
      GROUP BY c.planName, c.planId
      ORDER BY totalRevenue DESC
    `
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] getRevenueByPlanQuery - Uses: COUNT, SUM, GROUP BY, IN (aggregations and GROUP BY not supported in DynamoDB)')
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] Query:', query.replace(/\s+/g, ' ').trim())
    return query
  },

  getActiveSubscriptionFeatureAccessQuery: (organizationId) => {
    return `SELECT c.id, c.planName, c.features, c.addOnFeatures FROM c WHERE c.organizationId = '${organizationId}' AND c.status IN ('active', 'free trial', 'pending')`
  },

  // Additional subscription queries
  getAllPlansQuery: () => {
    return `SELECT * FROM c WHERE c.isActive = true AND c.id != '${ORGANIZATION_PLAN_ID}' ORDER BY c.created_on DESC`
  },

  getOrganizationSubscriptionQuery: (organizationId) => {
    return `
      SELECT * FROM c
      WHERE c.organizationId = '${organizationId}'
      AND c.status = 'active'
      ORDER BY c.created_on DESC
    `
  },

  getOrganizationSubscriptionHistoryQuery: (organizationId) => {
    return `
      SELECT * FROM c
      WHERE c.organizationId = '${organizationId}'
      ORDER BY c.created_on DESC
    `
  },

  getSubscriptionByIdQuery: (subscriptionId) => {
    return `SELECT * FROM c WHERE c.id = '${subscriptionId}'`
  },

  getExpiringSubscriptionsQuery: (daysBeforeExpiry = 7) => {
    const futureDate = getISTDateWithDays(daysBeforeExpiry)
    const currentISTDate = getISTISOString()

    return `
      SELECT * FROM c
      WHERE c.status = 'active'
      AND c.endDate <= '${futureDate.toISOString()}'
      AND c.endDate >= '${currentISTDate}'
      ORDER BY c.endDate ASC
    `
  },

  getExpiredSubscriptionsQuery: () => {
    const currentISTDate = getISTISOString()
    return `
      SELECT * FROM c
      WHERE (c.status = 'active' OR c.status = 'free trial' OR c.status = 'pending')
      AND c.endDate < '${currentISTDate}'
    `
  },

  getSubscriptionsByPlanIdQuery: (planId) => {
    return `SELECT * FROM c WHERE c.planId = "${planId}"`
  },

  hasUsedFreeTrialQuery: (contactEmail) => {
    return `SELECT * FROM c WHERE c.contactEmail = "${contactEmail}" AND c.trialUsed = true`
  },

  getActiveSubscriptionByEmailAndOrganizationQuery: (email, organizationId) => {
    return {
      query: `SELECT * FROM c WHERE c.organizationId = @organizationId
              AND c.contactEmail = @email
              AND (c.status = 'active' OR c.status = 'free trial' OR c.status = 'pending')
              ORDER BY c.created_on DESC`,
      parameters: [
        { name: '@organizationId', value: organizationId },
        { name: '@email', value: email },
      ],
    }
  },

  getAnySubscriptionByEmailAndOrganizationQuery: (email, organizationId) => {
    return {
      query: `SELECT * FROM c WHERE c.organizationId = @organizationId
              AND c.contactEmail = @email
              ORDER BY c.created_on DESC`,
      parameters: [
        { name: '@organizationId', value: organizationId },
        { name: '@email', value: email },
      ],
    }
  },

  getAnySubscriptionByEmailQuery: (email) => {
    return {
      query: `SELECT * FROM c WHERE c.contactEmail = @email
              ORDER BY c.created_on DESC`,
      parameters: [{ name: '@email', value: email }],
    }
  },

  getPlanByNameQuery: (planName, excludePlanId = null) => {
    let queryStr, params
    if (excludePlanId) {
      queryStr = `SELECT * FROM c WHERE LOWER(c.planName) = LOWER(@planName) AND c.id != @excludePlanId AND c.id != '${ORGANIZATION_PLAN_ID}'`
      params = [
        { name: '@planName', value: planName },
        { name: '@excludePlanId', value: excludePlanId },
      ]
    } else {
      queryStr = `SELECT * FROM c WHERE LOWER(c.planName) = LOWER(@planName) AND c.id != '${ORGANIZATION_PLAN_ID}'`
      params = [{ name: '@planName', value: planName }]
    }
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] getPlanByNameQuery - Uses: LOWER')
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][SUBSCRIPTIONS] Parameters:', JSON.stringify(params))
    return {
      query: queryStr,
      parameters: params,
    }
  },
}
