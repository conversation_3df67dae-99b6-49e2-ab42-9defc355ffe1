const { buildDateFilterClause } = require('../utils/query-utils')

// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

const getPrescriptionsByPatient = (
  patientId,
  dateFilter = null,
  customDateRange = null,
  searchText = null,
) => {
  let dateClause = buildDateFilterClause(dateFilter)

  if (dateFilter === 'custom' && customDateRange) {
    dateClause = `AND c.updated_on >= "${customDateRange.start}" AND c.updated_on <= "${customDateRange.end}"`
  }

  const searchClause = searchText
    ? `AND (
        CONTAINS(LOWER(c.doctor), LOWER("${searchText}"))
        OR ARRAY_LENGTH(
          ARRAY(
            SELECT VALUE m
            FROM m IN c.medicines
            WHERE CONTAINS(LOWER(m.genericName), LOWER("${searchText}"))
          )
        ) > 0
      )`
    : ''

  const query = `
    SELECT * FROM c
    WHERE c.patientId = "${patientId}"
    ${dateClause}
    ${searchClause}
  `

  // Log DynamoDB unsupported query features
  if (searchText) {
    console.log('[DYNAMODB_UNSUPPORTED][PRESCRIPTIONS] getPrescriptionsByPatient - Uses: CONTAINS, LOWER, ARRAY_LENGTH, ARRAY subquery, nested JOIN')
    console.log('[DYNAMODB_UNSUPPORTED][PRESCRIPTIONS] Query:', query.replace(/\s+/g, ' ').trim())
  }

  return query
}

const getPrescriptionById = (prescriptionId) => {
  return `SELECT * FROM c WHERE  c.id = "${prescriptionId}"`
}
const searchPrescriptionQuery = (queryString, patientId) => {
  const query = `
  SELECT * FROM c
  WHERE c.patientId = "${patientId}" AND (
    CONTAINS(LOWER(c.doctor), LOWER("${queryString}"))
    OR ARRAY_LENGTH(
      ARRAY(
        SELECT VALUE m
        FROM m IN c.medicines
        WHERE CONTAINS(LOWER(m.genericName), LOWER("${queryString}"))
      )
    ) > 0
  )
  ORDER BY c.updated_on DESC
`
  console.log('[DYNAMODB_UNSUPPORTED][PRESCRIPTIONS] searchPrescriptionQuery - Uses: CONTAINS, LOWER, ARRAY_LENGTH, ARRAY subquery, nested JOIN')
  console.log('[DYNAMODB_UNSUPPORTED][PRESCRIPTIONS] Query:', query.replace(/\s+/g, ' ').trim())
  return query
}
module.exports = {
  getPrescriptionsByPatient,
  getPrescriptionById,
  searchPrescriptionQuery,
}
