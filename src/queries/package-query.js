// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

const getPackageById = (packageId) => {
  return `SELECT * FROM c WHERE c.id = "${packageId}"`;
};

const getPackageByName = (name) => {
  const query = `SELECT * FROM c WHERE LOWER(c.name) = "${name.trim().toLowerCase()}"`;
  console.log('[DYNAMODB_UNSUPPORTED][PACKAGES] getPackageByName - Uses: LOWER');
  console.log('[DYNAMODB_UNSUPPORTED][PACKAGES] Query:', query);
  return query;
};

const getPackagesByType = (type) => {
  return `SELECT c.id, c.name, c.type FROM c WHERE c.type = "${type.trim()}" ORDER BY c.updated_on DESC`;
};

const getPackagesByTypeAndUser = (type, userId) => {
  const query = `SELECT c.id, c.name, c.type, c.createdBy, c.departmentName, c.description, c.isActive, c.created_on as createdAt, c.updated_on as updatedAt, ARRAY_LENGTH(c.medicines) as medicineCount FROM c WHERE c.type = "${type.trim()}" AND c.createdBy = "${userId}" AND c.isActive = true`;
  console.log('[DYNAMODB_UNSUPPORTED][PACKAGES] getPackagesByTypeAndUser - Uses: ARRAY_LENGTH, computed field (medicineCount)');
  console.log('[DYNAMODB_UNSUPPORTED][PACKAGES] Query:', query);
  return query;
};

const getPackageByNameAndUser = (name, userId, type) => {
  const query = `SELECT * FROM c WHERE LOWER(c.name) = "${name.trim().toLowerCase()}" AND c.createdBy = "${userId}" AND c.type = "${type}" AND c.isActive = true`;
  console.log('[DYNAMODB_UNSUPPORTED][PACKAGES] getPackageByNameAndUser - Uses: LOWER');
  console.log('[DYNAMODB_UNSUPPORTED][PACKAGES] Query:', query);
  return query;
};

const getPackageByNameAndType = (name, type) => {
  const query = `SELECT * FROM c WHERE LOWER(c.name) = "${name.trim().toLowerCase()}" AND c.type = "${type}" AND c.isActive = true`;
  console.log('[DYNAMODB_UNSUPPORTED][PACKAGES] getPackageByNameAndType - Uses: LOWER');
  console.log('[DYNAMODB_UNSUPPORTED][PACKAGES] Query:', query);
  return query;
};

const getPackageWithMedicines = (packageId) => {
  return `SELECT c.id, c.name, c.type, c.createdBy, c.departmentName, c.description, c.isActive, c.created_on as createdAt, c.updated_on as updatedAt, c.medicines, c.usageStatistics FROM c WHERE c.id = "${packageId}"`;
};

module.exports = {
  getPackageById,
  getPackageByName,
  getPackagesByType,
  getPackagesByTypeAndUser,
  getPackageByNameAndUser,
  getPackageByNameAndType,
  getPackageWithMedicines,
};
