// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

const getLabTestsByPatientIdQuery = (patientId) =>
  `SELECT * FROM c WHERE c.patientId = "${patientId}"`

const getLabTestById = (labTestId) =>
  `SELECT * FROM c WHERE c.id = "${labTestId}"`

const searchTestQuery = (queryString, patientId) => {
  const query = `
  SELECT c.id, c.patientId, test
  FROM c
  JOIN test IN c.labTests
  WHERE c.patientId = "${patientId}" AND (
    CONTAINS(LOWER(test.testName), LOWER("${queryString}"))
  )
`
  console.log('[DYNAMODB_UNSUPPORTED][PATIENT_LAB_TESTS] searchTestQuery - Uses: JOIN (nested array), CONTAINS, LOWER')
  console.log('[DYNAMODB_UNSUPPORTED][PATIENT_LAB_TESTS] Query:', query.replace(/\s+/g, ' ').trim())
  return query
}
const getLabTestsWithSortingAndFilteringQuery = (
  patientId,
  dateClause,
  sortClause,
  searchText,
  department,
) => {
  const searchClause = searchText
    ? `AND EXISTS(SELECT VALUE t FROM t IN c.labTests WHERE CONTAINS(LOWER(t.testName), LOWER("${searchText}")))`
    : ''

  const query = `
    SELECT * FROM c
    WHERE c.patientId = "${patientId}"
    ${dateClause}
    ${searchClause}
    ${sortClause}
  `

  // Log DynamoDB unsupported query features
  if (searchText) {
    console.log('[DYNAMODB_UNSUPPORTED][PATIENT_LAB_TESTS] getLabTestsWithSortingAndFilteringQuery - Uses: EXISTS subquery, CONTAINS, LOWER, nested array access')
    console.log('[DYNAMODB_UNSUPPORTED][PATIENT_LAB_TESTS] Query:', query.replace(/\s+/g, ' ').trim())
  }

  return query
}

const getPatientLatestLabTestStatusQuery = (patientId) => {
  return `SELECT TOP 1 c.labTests 
        FROM c 
        WHERE c.patientId = "${patientId}"
        ORDER BY c.updated_on DESC`
}

module.exports = {
  getLabTestsByPatientIdQuery,
  getLabTestById,
  searchTestQuery,
  getLabTestsWithSortingAndFilteringQuery,
  getPatientLatestLabTestStatusQuery
}
