// DynamoDB Migration Note: This file contains Cosmos DB-specific SQL queries
// that are NOT supported in DynamoDB and will need to be rewritten

module.exports = {
  getAllFeaturesQuery: (includeInactive = false) => {
    let query = 'SELECT * FROM c'
    if (!includeInactive) {
      query += ' WHERE c.isActive = true'
    }
    query += ' ORDER BY c.featureName ASC'
    return query
  },

  searchFeaturesQuery: (searchParams) => {
    const conditions = ['c.isActive = true']
    const parameters = []

    if (searchParams.featureName) {
      conditions.push('CONTAINS(LOWER(c.featureName), LOWER(@featureName))')
      parameters.push({ name: '@featureName', value: searchParams.featureName })
    }

    if (searchParams.permissionKey) {
      conditions.push('ARRAY_CONTAINS(c.permissionKeys, @permissionKey)')
      parameters.push({ name: '@permissionKey', value: searchParams.permissionKey })
    }

    const queryStr = `SELECT * FROM c WHERE ${conditions.join(' AND ')} ORDER BY c.created_on DESC`
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] searchFeaturesQuery - Uses: CONTAINS, LOWER, ARRAY_CONTAINS')
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] Query:', queryStr)
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] Parameters:', JSON.stringify(parameters))
    return {
      query: queryStr,
      parameters: parameters,
    }
  },

  getFeaturesByPermissionKeyQuery: (permissionKey) => {
    const query = `SELECT * FROM c WHERE c.isActive = true AND ARRAY_CONTAINS(c.permissionKeys, '${permissionKey}') ORDER BY c.featureName ASC`
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] getFeaturesByPermissionKeyQuery - Uses: ARRAY_CONTAINS')
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] Query:', query)
    return query
  },

  getAllPermissionKeysQuery: () => {
    const query = `SELECT DISTINCT VALUE pk FROM c JOIN pk IN c.permissionKeys WHERE c.isActive = true`
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] getAllPermissionKeysQuery - Uses: DISTINCT VALUE, JOIN (nested array flattening)')
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] Query:', query)
    return query
  },

  // Batch query to get features by multiple IDs
  getFeaturesByIdsQuery: (featureIds) => {
    if (!featureIds || featureIds.length === 0) return null
    const idList = featureIds.map(id => `'${id}'`).join(',')
    const query = `SELECT * FROM c WHERE c.id IN (${idList})`
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] getFeaturesByIdsQuery - Uses: IN with dynamic list')
    console.log('[DYNAMODB_UNSUPPORTED][FEATURES] Query:', query)
    return query
  },
}
