const { app } = require('@azure/functions')
const {
  BlobServiceClient,
  generateBlobSASQueryParameters,
  BlobSASPermissions,
  StorageSharedKeyCredential,
} = require('@azure/storage-blob')
const { encryptBuffer } = require('../services/encryptionService')
const {
  saveLabReportMetadata,
  updateLabReportMetadata,
  deleteLabReportMetadata,
} = require('../services/cosmosService')
const { v4: uuidv4 } = require('uuid')
const axios = require('axios')
const { getLabReportMetadata } = require('../services/cosmosService')
const { decryptBuffer } = require('../services/encryptionService')
const { fileTypeFromBuffer } = require('file-type')
const labTestService = require('../services/patient-lab-test-service')
const { LabTestStatus } = require('../common/constant')
const FormData = require('form-data')
const secretManager = require('../services/secret-manager')

async function processDocumentWithOCR(
  buffer,
  fileName,
  context,
  ocrServiceUrl,
) {
  try {
    context.log('Starting OCR processing for file:', fileName)

    const formData = new FormData()
    formData.append('file', buffer, {
      filename: fileName,
      contentType: 'application/pdf',
    })

    const response = await axios.post(ocrServiceUrl, formData, {
      headers: {
        ...formData.getHeaders(),
        Accept: '*/*',
        'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
        Connection: 'keep-alive',
        'User-Agent':
          'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      },
      timeout: 120000,
    })

    // Extract JSON data from HTML response
    const htmlContent = response.data
    context.log('OCR response type:', typeof htmlContent)
    context.log('OCR response length:', htmlContent?.length || 0)

    // Try multiple patterns to extract JSON data
    let jsonStr = null
    let parsedData = null

    // Pattern 1: data='...' or data="..."
    let jsonMatch = htmlContent.match(/data='(\{.*?\})'|data="(\{.*?\})"/s)
    if (jsonMatch) {
      context.log('Found JSON using Pattern 1 (quoted data)')
      jsonStr = jsonMatch[1] || jsonMatch[2]
    } else {
      // Pattern 2: data = {...} (without quotes)
      jsonMatch = htmlContent.match(/data\s*=\s*(\{[\s\S]*?\});/s)
      if (jsonMatch) {
        context.log('Found JSON using Pattern 2 (unquoted data)')
        jsonStr = jsonMatch[1]
      } else {
        // Pattern 3: Look for JSON-like structure with test_results
        jsonMatch = htmlContent.match(/(\{[\s\S]*?"test_results"[\s\S]*?\})/s)
        if (jsonMatch) {
          context.log('Found JSON using Pattern 3 (test_results structure)')
          jsonStr = jsonMatch[1]
        }
      }
    }

    if (!jsonStr) {
      throw new Error('Could not find JSON data in OCR response')
    }

    try {
      parsedData = JSON.parse(jsonStr)
    } catch (parseError) {
      context.error(
        'Failed to parse JSON from OCR response:',
        parseError.message,
      )
      throw new Error('Could not parse JSON data from OCR response')
    }

    context.log('OCR processing completed successfully')
    return { structured: { test_results: parsedData.test_results || [] } }
  } catch (error) {
    context.error('OCR processing failed:', error.message)
    throw error
  }
}

function calculateStringSimilarity(str1, str2) {
  const len1 = str1.length
  const len2 = str2.length

  if (len1 === 0) return len2 === 0 ? 1 : 0
  if (len2 === 0) return 0

  const matrix = Array(len1 + 1)
    .fill()
    .map(() => Array(len2 + 1).fill(0))

  for (let i = 0; i <= len1; i++) matrix[i][0] = i
  for (let j = 0; j <= len2; j++) matrix[0][j] = j

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost,
      )
    }
  }

  const maxLen = Math.max(len1, len2)
  return (maxLen - matrix[len1][len2]) / maxLen
}

async function updateLabTestsWithOCRResults(labTestId, ocrData, context) {
  try {
    context.log('Updating lab tests with OCR results for labTestId:', labTestId)

    const labTest = await labTestService.getLabTestById(labTestId)
    if (!labTest || labTest.length === 0) {
      context.error('Lab test not found:', labTestId)
      return {
        success: false,
        matchedTests: [],
        unmatchedTests: [],
        totalTests: 0,
      }
    }

    const labTestRecord = labTest[0]
    context.log(
      'Lab test record found:',
      JSON.stringify(labTestRecord, null, 2),
    )

    const ocrTestResults = ocrData.structured?.test_results || []

    if (ocrTestResults.length === 0) {
      context.warn('No OCR test results found in the data')
      return {
        success: false,
        matchedTests: [],
        unmatchedTests: labTestRecord.labTests.map((test) => test.testName),
        totalTests: labTestRecord.labTests.length,
      }
    }

    // Create a copy of used OCR results to avoid duplicate matching
    const availableOCRResults = [...ocrTestResults]
    let matchedCount = 0
    let updatedTests = []
    let matchedTestNames = []

    context.log(
      `Starting to process ${labTestRecord.labTests.length} lab tests`,
    )

    for (const test of labTestRecord.labTests) {
      context.log(`\n=== Processing test: "${test.testName}" ===`)

      let matchFound = false
      let matchingOCRTest = null
      let matchingIndex = -1

      // Try to find a matching OCR result
      for (let i = 0; i < availableOCRResults.length; i++) {
        const ocrTest = availableOCRResults[i]

        // Extract OCR test name with multiple field name variations
        let ocrTestName = ''
        if (ocrTest['Test Name']) {
          ocrTestName = ocrTest['Test Name']
        } else if (ocrTest.test_name) {
          ocrTestName = ocrTest.test_name
        } else if (ocrTest.testName) {
          ocrTestName = ocrTest.testName
        } else if (ocrTest.name) {
          ocrTestName = ocrTest.name
        }

        if (!ocrTestName) {
          context.log(`OCR test ${i + 1}: No test name found`)
          continue
        }

        context.log(`OCR test ${i + 1}: "${ocrTestName}"`)

        // Case-insensitive comparison
        const testNameLower = test.testName?.toLowerCase().trim() || ''
        const ocrTestNameLower = ocrTestName.toLowerCase().trim()

        // 1. Exact match (case insensitive)
        if (testNameLower === ocrTestNameLower) {
          context.log(`✓ EXACT MATCH found!`)
          matchFound = true
          matchingOCRTest = ocrTest
          matchingIndex = i
          break
        }

        // 2. Normalize and compare (remove special characters, extra spaces)
        const normalizeString = (str) => {
          return str
            .toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim()
        }

        const normalizedTestName = normalizeString(test.testName || '')
        const normalizedOCRName = normalizeString(ocrTestName)

        if (normalizedTestName === normalizedOCRName) {
          context.log(`✓ NORMALIZED MATCH found!`)
          matchFound = true
          matchingOCRTest = ocrTest
          matchingIndex = i
          break
        }

        // 3. Word-based partial match - check if all words from one appear in the other
        if (normalizedTestName.length > 5 && normalizedOCRName.length > 5) {
          // Split into words
          const testWords = normalizedTestName
            .split(/\s+/)
            .filter((w) => w.length > 0)
          const ocrWords = normalizedOCRName
            .split(/\s+/)
            .filter((w) => w.length > 0)

          // Critical medical qualifiers that must match exactly
          const criticalQualifiers = [
            'ldl',
            'hdl',
            'vldl',
            'total',
            'free',
            'direct',
            'indirect',
            'fasting',
            'random',
            'postprandial',
          ]

          // Check if one set of words is contained in the other
          const checkWordMatch = (shorterWords, longerWords) => {
            // Prevent single-word matches from matching multi-word tests
            // e.g., "Hemoglobin" should not match "Hemoglobin A1C"
            if (shorterWords.length === 1 && longerWords.length > 1) {
              return false
            }

            // All words from shorter must appear in longer
            const allWordsMatch = shorterWords.every((word) =>
              longerWords.includes(word),
            )

            if (!allWordsMatch) return false

            // Check for critical qualifiers - if present in one, must be present in the other
            for (const qualifier of criticalQualifiers) {
              const inShorter = shorterWords.includes(qualifier)
              const inLonger = longerWords.includes(qualifier)

              // If qualifier is in one but not the other, it's not a match
              if (inShorter !== inLonger) {
                return false
              }
            }

            return true
          }

          let isMatch = false
          if (testWords.length <= ocrWords.length) {
            isMatch = checkWordMatch(testWords, ocrWords)
          } else {
            isMatch = checkWordMatch(ocrWords, testWords)
          }

          if (isMatch) {
            context.log(`✓ WORD-BASED PARTIAL MATCH found!`)
            matchFound = true
            matchingOCRTest = ocrTest
            matchingIndex = i
            break
          }
        }

        // 4. Similarity match for longer names
        if (normalizedTestName.length > 8 && normalizedOCRName.length > 8) {
          const similarity = calculateStringSimilarity(
            normalizedTestName,
            normalizedOCRName,
          )
          context.log(`Similarity score: ${similarity.toFixed(2)}`)

          if (similarity >= 0.75) {
            // Before accepting similarity match, check for critical qualifiers
            const testWords = normalizedTestName
              .split(/\s+/)
              .filter((w) => w.length > 0)
            const ocrWords = normalizedOCRName
              .split(/\s+/)
              .filter((w) => w.length > 0)
            const criticalQualifiers = [
              'ldl',
              'hdl',
              'vldl',
              'total',
              'free',
              'direct',
              'indirect',
              'fasting',
              'random',
              'postprandial',
            ]

            // Check if both have the same critical qualifiers
            let hasMismatchedQualifier = false
            for (const qualifier of criticalQualifiers) {
              const inTest = testWords.includes(qualifier)
              const inOCR = ocrWords.includes(qualifier)

              // If qualifier is in one but not the other, reject the match
              if (inTest !== inOCR) {
                context.log(
                  `✗ SIMILARITY MATCH REJECTED: Mismatched qualifier "${qualifier}"`,
                )
                hasMismatchedQualifier = true
                break
              }
            }

            if (!hasMismatchedQualifier) {
              // 75% similarity threshold
              context.log(
                `✓ SIMILARITY MATCH found! (${similarity.toFixed(2)})`,
              )
              matchFound = true
              matchingOCRTest = ocrTest
              matchingIndex = i
              break
            }
          }
        }
      }

      if (matchFound && matchingOCRTest) {
        context.log(`\n🎯 MATCH CONFIRMED for "${test.testName}"`)
        context.log(
          `Matched with OCR: "${JSON.stringify(matchingOCRTest, null, 2)}"`,
        )

        // Remove the matched OCR result to avoid duplicate matching
        availableOCRResults.splice(matchingIndex, 1)
        matchedCount++
        matchedTestNames.push(test.testName)

        // Extract result value with multiple field name variations
        const ocrValue =
          matchingOCRTest.result ||
          matchingOCRTest.value ||
          matchingOCRTest.Value ||
          matchingOCRTest.Result ||
          ''

        // Extract reference interval with multiple field name variations
        const ocrReference =
          matchingOCRTest.reference_interval ||
          matchingOCRTest['Reference Interval'] ||
          matchingOCRTest.referenceInterval ||
          matchingOCRTest.reference ||
          matchingOCRTest.Reference ||
          ''

        // Update the test with OCR data and set status to READY
        const updatedTest = {
          ...test,
          results: ocrValue || test.results,
          reference: ocrReference || test.reference,
          status: LabTestStatus.READY,
        }

        updatedTests.push(updatedTest)
        context.log(
          `✅ Test updated successfully - Status: ${updatedTest.status}`,
        )
      } else {
        context.log(`❌ NO MATCH found for "${test.testName}"`)
        // Keep the original test unchanged
        updatedTests.push(test)
      }
    }

    // Update the lab test record with the processed tests
    labTestRecord.labTests = updatedTests

    if (matchedCount > 0) {
      await labTestService.updateLabTest(labTestRecord)

      // Log the final status of each test
      labTestRecord.labTests.forEach((test, index) => {
        context.log(
          `   Test ${index + 1}: "${test.testName}" - Status: ${test.status}`,
        )
      })
    } else {
      context.log('❌ No matches found - lab test record not updated')
    }

    const finalUnmatchedTests = labTestRecord.labTests
      .filter((test) => test.status !== LabTestStatus.READY)
      .map((test) => test.testName)
    context.log(`\n📋 FINAL UNMATCHED TESTS (based on final status):`)
    if (finalUnmatchedTests.length > 0) {
      finalUnmatchedTests.forEach((testName, index) => {
        context.log(`   ${index + 1}. ${testName}`)
      })
    } else {
      context.log('   None - all tests are now in Ready status')
    }

    return {
      success: matchedCount > 0,
      matchedTests: labTestRecord.labTests
        .filter((test) => test.status === LabTestStatus.READY)
        .map((test) => test.testName),
      unmatchedTests: finalUnmatchedTests,
      totalTests: labTestRecord.labTests.length,
      matchedCount: labTestRecord.labTests.filter(
        (test) => test.status === LabTestStatus.READY,
      ).length,
    }
  } catch (error) {
    context.error('Failed to update lab tests with OCR results:', error.message)
    context.error('Error stack:', error.stack)
    return {
      success: false,
      matchedTests: [],
      unmatchedTests: [],
      totalTests: 0,
    }
  }
}

app.http('lab-report-upload', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'lab-report/upload',
  handler: async (req, context) => {
    try {
      const connectionString = await secretManager.getSecret(
        'AzureWebJobsStorage',
      )
      const accountName = await secretManager.getSecret(
        'AZURE_STORAGE_ACCOUNT_NAME',
      )
      const accountKey = await secretManager.getSecret(
        'AZURE_STORAGE_ACCOUNT_KEY',
      )

      context.log('Environment check:', {
        hasStorage: !!connectionString,
        hasAccountName: !!accountName,
        hasAccountKey: !!accountKey,
        nodeVersion: process.version,
        platform: process.platform,
      })

      const formData = await req.formData()
      const files = formData.getAll('files')
      const patientId = formData.get('patientId')
      const labTestId = formData.get('labTestId')
      const fileIdsToRemove = formData.get('fileIdsToRemove')

      if (!files || files.length === 0 || !patientId || !labTestId) {
        return {
          status: 400,
          jsonBody: { error: 'Missing files, patientId, or labTestId' },
        }
      }

      // Remove old file metadata if provided
      const removedFileIds = []
      if (fileIdsToRemove) {
        try {
          const idsToRemove = JSON.parse(fileIdsToRemove)
          if (Array.isArray(idsToRemove) && idsToRemove.length > 0) {
            context.log(
              `Removing ${idsToRemove.length} old file metadata records`,
            )

            for (const fileId of idsToRemove) {
              try {
                await deleteLabReportMetadata(fileId)
                removedFileIds.push(fileId)
                context.log(`Successfully removed file metadata: ${fileId}`)
              } catch (error) {
                context.log(
                  `Failed to remove file metadata ${fileId}:`,
                  error.message,
                )
              }
            }
          }
        } catch (error) {
          context.log('Error parsing fileIdsToRemove:', error.message)
        }
      }

      const uploadedMetadata = []

      for (const file of files) {
        if (!(file instanceof File) && !(file instanceof Blob)) {
          return {
            status: 400,
            jsonBody: { error: 'Invalid file object received' },
          }
        }

        if (!file.size || file.size === 0) {
          return {
            status: 400,
            jsonBody: { error: 'Empty file not allowed' },
          }
        }
        async function safeArrayBuffer(file, retries = 3) {
          for (let i = 0; i < retries; i++) {
            try {
              context.log(`ArrayBuffer attempt ${i + 1}`)

              const arrayBuffer = await file.arrayBuffer()

              if (!arrayBuffer) {
                throw new Error('ArrayBuffer is null or undefined')
              }

              if (arrayBuffer.byteLength === 0) {
                throw new Error('ArrayBuffer is empty')
              }

              context.log(
                `ArrayBuffer success: ${arrayBuffer.byteLength} bytes`,
              )
              return arrayBuffer
            } catch (error) {
              context.log(`ArrayBuffer attempt ${i + 1} failed:`, error.message)

              if (i === retries - 1) {
                throw new Error(
                  `Failed to read file after ${retries} attempts: ${error.message}`,
                )
              }

              await new Promise((resolve) => setTimeout(resolve, 100 * (i + 1)))
            }
          }
        }

        async function fallbackFileRead(file) {
          context.log('Using fallback file reading method')

          const chunks = []
          const reader = file.stream().getReader()

          try {
            while (true) {
              const { done, value } = await reader.read()
              if (done) break
              chunks.push(value)
            }

            const totalLength = chunks.reduce(
              (acc, chunk) => acc + chunk.length,
              0,
            )
            const buffer = Buffer.concat(
              chunks.map((chunk) => Buffer.from(chunk)),
              totalLength,
            )

            context.log(`Fallback read success: ${buffer.length} bytes`)
            return buffer
          } catch (streamError) {
            throw new Error(
              `Fallback file reading failed: ${streamError.message}`,
            )
          } finally {
            reader.releaseLock()
          }
        }
        let buffer
        try {
          const arrayBuffer = await safeArrayBuffer(file)
          buffer = Buffer.from(arrayBuffer)
        } catch (arrayBufferError) {
          context.log(
            'ArrayBuffer method failed, trying fallback:',
            arrayBufferError.message,
          )
          buffer = await fallbackFileRead(file)
        }

        if (!buffer || buffer.length === 0) {
          return {
            status: 400,
            jsonBody: { error: 'File buffer is empty or invalid' },
          }
        }

        // Encrypt the buffer
        const { encryptedData, encryptionKey, iv } = await encryptBuffer(buffer)
        const fileId = uuidv4()
        const blobName = `patients/${patientId}/labtest/${labTestId}/${fileId}-${file.name}`

        // Azure Blob Storage setup
        const containerName = 'lab-reports'

        const blobServiceClient =
          BlobServiceClient.fromConnectionString(connectionString)
        const containerClient =
          blobServiceClient.getContainerClient(containerName)
        await containerClient.createIfNotExists()

        // Generate SAS URL for upload
        const sharedKeyCredential = new StorageSharedKeyCredential(
          accountName,
          accountKey,
        )

        const sasToken = generateBlobSASQueryParameters(
          {
            containerName,
            blobName,
            permissions: BlobSASPermissions.parse('cw'),
            expiresOn: new Date(Date.now() + 10 * 60 * 1000),
          },
          sharedKeyCredential,
        ).toString()
        console.log('nnnnnnnnnnnnnnnn')

        const uploadUrl = `https://${accountName}.blob.core.windows.net/${containerName}/${blobName}?${sasToken}`

        await axios.put(uploadUrl, encryptedData, {
          headers: {
            'x-ms-blob-type': 'BlockBlob',
            'Content-Length': encryptedData.length,
          },
          timeout: 30000,
        })

        const metadata = await saveLabReportMetadata({
          id: fileId,
          fileName: file.name,
          fileSize: file.size,
          blobPath: blobName,
          encryptionKey,
          iv,
          patientId,
          labTestId,
          ocrStatus: 'pending',
          detectedLanguage: null,
          ocrData: null,
          fileType: file.type,
          uploadedAt: new Date().toISOString(),
        })

        uploadedMetadata.push(metadata)

        try {
          context.log('Starting OCR processing for file:', file.name)

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'processing',
          })

          const ocrServiceUrl = process.env.OCR_SERVICE_URL
          console.log(ocrServiceUrl, 'llllllllllllllllllllll')

          const ocrData = await processDocumentWithOCR(
            buffer,
            file.name,
            context,
            ocrServiceUrl,
          )

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'completed',
            ocrData: ocrData,
          })

          context.log(
            'OCR processing completed successfully for file:',
            file.name,
          )
        } catch (ocrError) {
          context.error(
            'OCR processing failed for file:',
            file.name,
            ocrError.message,
          )

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'failed',
            ocrData: null,
          })
        }
      }

      const labTest = await labTestService.getLabTestById(labTestId)
      if (labTest && labTest.length > 0) {
        labTest[0].labTests = labTest[0].labTests.map((test) => {
          return {
            ...test,
            // Preserve READY status - only update status to UPLOADED if it's not already READY
            status:
              test.status === LabTestStatus.READY
                ? LabTestStatus.READY
                : LabTestStatus.UPLOADED,
            fileMetadata: uploadedMetadata.filter(
              (meta) => meta.labTestId === test.id,
            ),
          }
        })
        await labTestService.updateLabTest(labTest[0])
      }

      try {
        context.log('Processing OCR results for lab tests')

        const completedOCRFiles = uploadedMetadata.filter(
          (metadata) => metadata.ocrStatus === 'completed',
        )

        context.log(
          `Found ${completedOCRFiles.length} files with completed OCR out of ${uploadedMetadata.length} total files`,
        )

        if (completedOCRFiles.length === 0) {
          context.warn('No files with completed OCR found')
          // Wait a bit and try to process any files that might have completed OCR
          await new Promise((resolve) => setTimeout(resolve, 2000))

          // Re-check for completed OCR files
          const recheckFiles = []
          for (const metadata of uploadedMetadata) {
            try {
              const latestMetadata = await getLabReportMetadata(metadata.id)
              if (
                latestMetadata &&
                latestMetadata.ocrStatus === 'completed' &&
                latestMetadata.ocrData
              ) {
                recheckFiles.push(latestMetadata)
              }
            } catch (error) {
              context.error(
                `Error rechecking metadata for ${metadata.fileName}:`,
                error.message,
              )
            }
          }

          context.log(
            `After recheck, found ${recheckFiles.length} files with completed OCR`,
          )

          if (recheckFiles.length === 0) {
            context.warn(
              'Still no files with completed OCR found after recheck',
            )
            // Continue with the normal flow - don't return early
            // OCR might still be processing
          }

          // Process the rechecked files
          for (const latestMetadata of recheckFiles) {
            try {
              context.log(
                'Processing OCR results for file (recheck):',
                latestMetadata.fileName,
              )

              const ocrResult = await updateLabTestsWithOCRResults(
                labTestId,
                latestMetadata.ocrData,
                context,
              )

              // Update metadata with proper OCR status based on results
              let newOcrStatus = 'failed'
              if (ocrResult.totalTests > 0) {
                if (ocrResult.matchedCount === ocrResult.totalTests) {
                  newOcrStatus = 'completed'
                } else if (ocrResult.matchedCount > 0) {
                  newOcrStatus = 'partial'
                } else {
                  newOcrStatus = 'failed'
                }
              }

              // Update metadata with new OCR status
              await updateLabReportMetadata({
                ...latestMetadata,
                ocrStatus: newOcrStatus,
                ocrResult: {
                  matchedTests: ocrResult.matchedTests,
                  unmatchedTests: ocrResult.unmatchedTests,
                  totalTests: ocrResult.totalTests,
                  matchedCount: ocrResult.matchedCount,
                },
              })

              if (ocrResult.success) {
                context.log(
                  `Successfully updated ${ocrResult.matchedCount}/${ocrResult.totalTests} lab tests with OCR results for file:`,
                  latestMetadata.fileName,
                )
              } else {
                context.log(
                  `Failed to match any lab tests with OCR results for file: ${
                    latestMetadata.fileName
                  }. Unmatched tests: ${ocrResult.unmatchedTests.join(', ')}`,
                )
              }
            } catch (ocrProcessError) {
              context.error(
                'Error processing OCR results for file (recheck):',
                latestMetadata.fileName,
                ocrProcessError.message,
              )
            }
          }
        } else {
          // Process files that already have completed OCR
          for (const metadata of completedOCRFiles) {
            try {
              const latestMetadata = await getLabReportMetadata(metadata.id)

              if (
                latestMetadata &&
                latestMetadata.ocrStatus === 'completed' &&
                latestMetadata.ocrData
              ) {
                context.log(
                  'Processing OCR results for file:',
                  latestMetadata.fileName,
                )

                const ocrResult = await updateLabTestsWithOCRResults(
                  labTestId,
                  latestMetadata.ocrData,
                  context,
                )

                // Update metadata with proper OCR status based on results
                let newOcrStatus = 'failed'
                if (ocrResult.totalTests > 0) {
                  if (ocrResult.matchedCount === ocrResult.totalTests) {
                    newOcrStatus = 'completed'
                  } else if (ocrResult.matchedCount > 0) {
                    newOcrStatus = 'partial'
                  } else {
                    newOcrStatus = 'failed'
                  }
                }

                // Update metadata with new OCR status
                await updateLabReportMetadata({
                  ...latestMetadata,
                  ocrStatus: newOcrStatus,
                  ocrResult: {
                    matchedTests: ocrResult.matchedTests,
                    unmatchedTests: ocrResult.unmatchedTests,
                    totalTests: ocrResult.totalTests,
                    matchedCount: ocrResult.matchedCount,
                  },
                })

                if (ocrResult.success) {
                  context.log(
                    `Successfully updated ${ocrResult.matchedCount}/${ocrResult.totalTests} lab tests with OCR results for file:`,
                    latestMetadata.fileName,
                  )
                } else {
                  context.log(
                    `Failed to match any lab tests with OCR results for file: ${
                      latestMetadata.fileName
                    }. Unmatched tests: ${ocrResult.unmatchedTests.join(', ')}`,
                  )
                }
              } else {
                context.log(
                  'OCR not completed or no data available for file:',
                  metadata.fileName,
                )
              }
            } catch (ocrProcessError) {
              context.error(
                'Error processing OCR results for file:',
                metadata.fileName,
                ocrProcessError.message,
              )
            }
          }
        }
      } catch (ocrProcessingError) {
        context.error(
          'Error in OCR results processing:',
          ocrProcessingError.message,
        )
      }

      context.log('Upload completed successfully')

      // Get the latest metadata to check OCR status
      const latestMetadata = []
      for (const meta of uploadedMetadata) {
        const updated = await getLabReportMetadata(meta.id)
        if (updated) {
          latestMetadata.push(updated)
        } else {
          latestMetadata.push(meta)
        }
      }

      // Analyze OCR results from metadata
      const completedOCRFiles = latestMetadata.filter(
        (meta) => meta.ocrStatus === 'completed',
      )
      const failedOCRFiles = latestMetadata.filter(
        (meta) => meta.ocrStatus === 'failed',
      )
      const partialOCRFiles = latestMetadata.filter(
        (meta) => meta.ocrStatus === 'partial',
      )

      let ocrStatus = 'processing'
      let matchedTestsCount = 0
      let totalTestsCount = 0
      let allMatchedTests = []
      let allUnmatchedTests = []

      // Get final state directly from database instead of aggregating from individual file metadata
      // This ensures we get the actual current status after all processing is complete
      let finalLabTestState = null
      try {
        const labTest = await labTestService.getLabTestById(labTestId)
        if (labTest && labTest.length > 0) {
          finalLabTestState = labTest[0]
        }
      } catch (error) {
        context.error('Error fetching final lab test state:', error.message)
      }

      if (finalLabTestState) {
        // Determine matched and unmatched based on FINAL status in database
        const readyTests = finalLabTestState.labTests.filter(
          (test) => test.status === LabTestStatus.READY,
        )
        const notReadyTests = finalLabTestState.labTests.filter(
          (test) => test.status !== LabTestStatus.READY,
        )

        allMatchedTests = readyTests.map((test) => test.testName)
        allUnmatchedTests = notReadyTests.map((test) => test.testName)
        matchedTestsCount = readyTests.length
        totalTestsCount = finalLabTestState.labTests.length

        context.log(`\n🔍 FINAL DATABASE STATE:`)
        context.log(`   Total tests: ${totalTestsCount}`)
        context.log(`   Ready tests: ${matchedTestsCount}`)
        context.log(`   Not ready tests: ${allUnmatchedTests.length}`)
        context.log(`   Matched tests: [${allMatchedTests.join(', ')}]`)
        context.log(`   Unmatched tests: [${allUnmatchedTests.join(', ')}]`)
      } else {
        // Fallback to aggregation if database fetch fails
        context.warn(
          'Could not fetch final lab test state, falling back to metadata aggregation',
        )
        for (const meta of latestMetadata) {
          if (meta.ocrResult) {
            matchedTestsCount += meta.ocrResult.matchedCount || 0
            totalTestsCount = Math.max(
              totalTestsCount,
              meta.ocrResult.totalTests || 0,
            )
            if (meta.ocrResult.matchedTests) {
              allMatchedTests = [
                ...new Set([
                  ...allMatchedTests,
                  ...meta.ocrResult.matchedTests,
                ]),
              ]
            }
            if (meta.ocrResult.unmatchedTests) {
              allUnmatchedTests = [
                ...new Set([
                  ...allUnmatchedTests,
                  ...meta.ocrResult.unmatchedTests,
                ]),
              ]
            }
          }
        }
      }

      // Determine overall OCR status based on FINAL RESULTS, not individual file status
      if (
        completedOCRFiles.length > 0 ||
        failedOCRFiles.length > 0 ||
        partialOCRFiles.length > 0
      ) {
        // Check final results: if all tests are matched, status should be 'completed'
        if (
          totalTestsCount > 0 &&
          matchedTestsCount === totalTestsCount &&
          allUnmatchedTests.length === 0
        ) {
          ocrStatus = 'completed'
          context.log(
            `🎯 Overall OCR status: COMPLETED (all ${totalTestsCount} tests matched)`,
          )
        } else if (matchedTestsCount > 0) {
          ocrStatus = 'partial'
          context.log(
            `⚠️ Overall OCR status: PARTIAL (${matchedTestsCount}/${totalTestsCount} tests matched)`,
          )
        } else {
          ocrStatus = 'failed'
          context.log(`❌ Overall OCR status: FAILED (no tests matched)`)
        }
      }

      // If OCR failed completely (no matches found), return error
      if (ocrStatus === 'failed' && allUnmatchedTests.length > 0) {
        return {
          status: 400,
          jsonBody: {
            error: 'Lab report processed but no matching tests found',
            message:
              'The uploaded lab report was processed successfully, but none of the test names in the report match the lab tests in your order. Please verify that the lab report corresponds to the correct lab test order.',
            uploadedFiles: uploadedMetadata.length,
            labTestId: labTestId,
            ocrStatus: 'failed',
            matchedTests: allMatchedTests,
            unmatchedTests: allUnmatchedTests,
            matchedCount: matchedTestsCount,
            totalTests: totalTestsCount,
            metadata: latestMetadata.map((meta) => ({
              id: meta.id,
              fileName: meta.fileName,
              fileSize: meta.fileSize,
              fileType: meta.fileType,
              uploadedAt: meta.uploadedAt,
              ocrStatus: meta.ocrStatus,
            })),
            removedFileIds: removedFileIds,
            removedCount: removedFileIds.length,
          },
        }
      }

      return {
        status: 200,
        jsonBody: {
          message:
            ocrStatus === 'completed'
              ? `Upload successful - ${matchedTestsCount} lab tests updated`
              : ocrStatus === 'partial'
              ? `Upload successful - ${matchedTestsCount}/${totalTestsCount} lab tests updated (partial match)`
              : ocrStatus === 'failed'
              ? 'Upload successful - OCR processing failed'
              : 'Upload successful - OCR processing in progress',
          uploadedFiles: uploadedMetadata.length,
          labTestId: labTestId,
          ocrStatus: ocrStatus,
          matchedTests: allMatchedTests,
          unmatchedTests: allUnmatchedTests,
          matchedCount: matchedTestsCount,
          totalTests: totalTestsCount,
          metadata: latestMetadata.map((meta) => ({
            id: meta.id,
            fileName: meta.fileName,
            fileSize: meta.fileSize,
            fileType: meta.fileType,
            uploadedAt: meta.uploadedAt,
            ocrStatus: meta.ocrStatus,
          })),
          removedFileIds: removedFileIds,
          removedCount: removedFileIds.length,
        },
      }
    } catch (error) {
      context.error('Upload handler error:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      })

      return {
        status: 500,
        jsonBody: {
          error: 'Internal server error',
          detail: error.message,
          timestamp: new Date().toISOString(),
        },
      }
    }
  },
})

app.http('lab-report-decrypt', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'lab-report/preview',
  handler: async (req, context) => {
    const docId = req.query.get('docId')
    const metadata = await getLabReportMetadata(docId)

    const accountName = await secretManager.getSecret(
      'AZURE_STORAGE_ACCOUNT_NAME',
    )
    const accountKey = await secretManager.getSecret(
      'AZURE_STORAGE_ACCOUNT_KEY',
    )

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey,
    )

    const sasToken = generateBlobSASQueryParameters(
      {
        containerName: 'lab-reports',
        blobName: metadata.blobPath,
        permissions: BlobSASPermissions.parse('r'),
        expiresOn: new Date(Date.now() + 5 * 60 * 1000),
      },
      sharedKeyCredential,
    ).toString()

    const downloadUrl = `https://${accountName}.blob.core.windows.net/lab-reports/${metadata.blobPath}?${sasToken}`
    const blobRes = await axios.get(downloadUrl, {
      responseType: 'arraybuffer',
    })

    const decryptedBuffer = await decryptBuffer(
      Buffer.from(blobRes.data),
      metadata.encryptionKey,
      metadata.iv,
    )

    const fileType = await fileTypeFromBuffer(decryptedBuffer)

    return {
      status: 200,
      headers: { 'Content-Type': fileType.mime },
      body: decryptedBuffer,
    }
  },
})
