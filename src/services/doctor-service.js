const cosmosDbContext = require('../cosmosDbContext/comosdb-context');
const logging = require('../common/logging');
const { encryptData, decryptData } = require('../common/helper');
const _ = require('lodash');
const DoctorProfileModel = require('../models/doctor-model');
const containerId = "Doctors"
const containerDoctorPatientId = "DoctorPatients"
const patientSummaryContainerId = "PatientSummaries"
const doctorCustomiseEmrContainerId = "DoctorCustomiseEmrs"

class DoctorService {
  /**
   * Create a doctor
   * @param {*} doctor 
   * @returns any
   */
  async createDoctor(doctor) {
    try {
      const doctorProfile = mapDoctor(doctor);
      var result = await cosmosDbContext.createItem(doctorProfile, containerId);
      var data = new DoctorProfileModel(result);
      var finalData = decryptDoctor(data);
      return finalData;
    } catch (error) {
      logging.logError("unable to create doctor", error);
      return null;
    }
  }
  /**
   * Get doctor by ID
   * @param {*} id string
   * @returns any
   */
  async getDoctor(id) {
    try {
      var result = await cosmosDbContext.readItem(id, id, containerId);
      if (!result) {
        return null;
      }
      var doctor = new DoctorProfileModel(result);
      var finalData = decryptDoctor(doctor);
      return finalData;
    } catch (error) {
      logging.logError("unable to get doctor", error);
      return null;
    }
  }

  /**
   * Get doctors by query string
   * @param {query} query string 
   * @returns any
   */
  async queryDoctors(query, pageSize = 10, continueToken = null) {
    try {
      var result = await cosmosDbContext.getAllItemQuery(containerId, query, pageSize, continueToken);

      // Check if any doctors were found
      if (!result.items || result.items.length === 0) {
        return null;
      }

      var datadecrypt = new DoctorProfileModel(result.items[0]);
      var finalData = decryptDoctor(datadecrypt);
      return finalData;
    } catch (error) {
      logging.logError("Unable to get doctors", error);
      return null;
    }
  }

  /**
   * Get all doctors with paging, default is 10 records per query
   * @param {*} pageSize the number of record will be returned, default is 10
   * @param {*} continueToken to query next page
   * @returns 
   */
  async getAllDoctors(pageSize, continueToken) {
    try {
      var result = await cosmosDbContext.getAllItems(containerId, pageSize, continueToken)
      var finalData = decryptDoctor(result);
      return finalData;
    } catch (error) {
      logging.logError("Unable to get doctors", error);
      return null;
    }
  }

  async getPatientByDoctor(doctorId) {
    try {
      var query = `SELECT * FROM c WHERE c.doctorId = '${doctorId}'`
      var data = await cosmosDbContext.queryItems(query, containerDoctorPatientId)
      var finalData = decryptDoctor(data);
      return finalData;
    } catch (error) {
      logging.logError(`Unable to get patient with docterid :: ${doctorId}`);
    }
  }

  /**
   * Update doctor
   * @param {*} doctor 
   * @returns any
   */
  async updateDoctor(doctor) {
    try {
      const doctorProfile = mapDoctor(doctor);
      var result = await cosmosDbContext.updateItem(doctorProfile, containerId);
      return result;
    } catch (error) {
      logging.logError("Unable to update doctor", error);
      return null;
    }
  }

  async upsertDoctor(id, updateData) {
    try {
      //const doctorProfile = mapDoctor(updateData);
      //const patchData = {...doctorProfile, updated_on: new Date().toISOString() }
      const patchData = encryptDoctor(updateData);
      const result = await cosmosDbContext.patchItem(id, patchData, containerId);
      const Data = new DoctorProfileModel(result);
      var finalData = decryptDoctor(Data);
      return finalData;
    } catch (error) {
      logging.logError(`Unable to upsert doctor: ${id}`, error);
      return null;
    }
  }

  async createDoctorPatient(doctorPatient) {
    try {
      var result = await cosmosDbContext.createItem(doctorPatient, containerDoctorPatientId);
      return result
    } catch (error) {
      logging.logError('Unable to create doctorpatient', error);
      return null;
    }
  }

  async updateDoctorPatient(doctorPatient) {
    try {
      var result = await cosmosDbContext.updateItem(doctorPatient, containerDoctorPatientId);
      return result;
    } catch (error) {
      logging.logError('Unable to update doctorpatient', error);
      return null;
    }
  }

  /**
   * Delete doctor
   * @param {*} id 
   * @returns 
   */
  async deleteDoctor(id) {
    try {
      var result = await cosmosDbContext.deleteItem(id, id, containerId);
      return result;
    } catch (error) {
      logging.logError("Unable to update doctor", error);
      return null;
    }
  }

  async createDoctorSumamry(summary) {
    try {
      var result = await cosmosDbContext.createItem(summary, patientSummaryContainerId);
      return result
    } catch (error) {
      logging.logError('Unable to create doctorpatient', error);
      return null;
    }
  }

  async getDoctorSummary(patientId) {
    try {
      var query = `SELECT * FROM c WHERE c.patientId = '${patientId}'`
      var data = await cosmosDbContext.queryItems(query, patientSummaryContainerId)
      return data;
    } catch (error) {
      logging.logError(`Unable to get patient with docterid :: ${doctorId}`);
      return null;
    }
  }

  async upsertDoctorSummary(id, updateData) {
    try {
      const result = await cosmosDbContext.patchItem(id, updateData, patientSummaryContainerId);
      return result;
    } catch (error) {
      logging.logError(`Unable to upsert doctor: ${id}`, error);
      return null;
    }
  }

  async createDoctorCustomiseEmr(customiseEmr) {
    try {
      var result = await cosmosDbContext.createItem(customiseEmr, doctorCustomiseEmrContainerId);
      return result
    } catch (error) {
      logging.logError('Unable to create doctorpatient', error);
      return null;
    }
  }

  async getDoctorCustomiseEmr(doctorId) {
    try {
      var query = `SELECT * FROM c WHERE c.doctorId = '${doctorId}'`
      var data = await cosmosDbContext.queryItems(query, doctorCustomiseEmrContainerId)
      return data;
    } catch (error) {
      logging.logError(`Unable to get patient with docterid :: ${doctorId}`);
      return null;
    }
  }

  async patchDoctorCustomiseEmr(id, updateData) {
    try {
      const result = await cosmosDbContext.patchItem(id, updateData, doctorCustomiseEmrContainerId);
      return result;
    } catch (error) {
      logging.logError(`Unable to upsert doctor: ${id}`, error);
      return null;
    }
  }
}

module.exports = new DoctorService();

function mapDoctor(doctor) {
  const doctorWithProfile = { ...doctorProfile, ...doctor };
  return encryptDoctor(doctorWithProfile, sensitiveFields);
}

function decryptDoctor(data) {
  const result = _.cloneDeep(data); // Avoid mutating the original object

  sensitiveFields.forEach((field) => {
    if (field.includes("[]")) {
      const [arrayField, subField] = field.split("[]");
      const array = _.get(result, arrayField, []);
      if (Array.isArray(array)) {
        array.forEach((item, index) => {
          const value = _.get(item, subField);
          if (value) {
            _.set(result, `${arrayField}[${index}].${subField}`, decryptDeterministic(value));
          }
        });
      }
    } else {
      const value = _.get(result, field);
      if (value) {
        _.set(result, field, decryptData(value));
      }
    }
  });

  return result;
}

function encryptDoctor(data) {
  const result = _.cloneDeep(data); // Avoid mutating the original object

  sensitiveFields.forEach((field) => {
    if (field.includes("[]")) {
      const [arrayField, subField] = field.split("[]");
      const array = _.get(result, arrayField, []);
      if (Array.isArray(array)) {
        array.forEach((item, index) => {
          const value = _.get(item, subField);
          if (value) {
            _.set(result, `${arrayField}[${index}].${subField}`, encryptData(value));
          }
        });
      }
    } else {
      const value = _.get(result, field);
      if (value) {
        _.set(result, field, encryptData(value));
      }
    }
  });

  return result;
}

const doctorProfile = {
  "id": "",
  "username": "",
  "general": {
    "fullName": "",
    "designation": "",
    "department": "",
    "doctorID": "",
    "contactNumber": "",
    "workEmail": ""
  },
  "personal": {
    "age": "",
    "bloodGroup": "",
    "height": "",
    "weight": "",
    "isPersonWithDisability": "",
    "percentOfDisability": "",
    "identificationMark": "",
    "maritalStatus": "",
    "dateOfWedding": "",
    "nationality": "",
    "religion": "",
    "caste": "",
    "category": "",
    "reservationDetails": "",
    "idProof": {
      "type": "",
      "number": "",
      "description": "",
      "url": ""
    },
    "placeOfBirth": {
      "hometown": "",
      "state": "",
      "district": "",
      "country": ""
    },
    "address": {
      "permanent": {
        "home": "",
        "street": "",
        "city": "",
        "pinCode": "",
        "district": "",
        "state": "",
        "country": "",
        "phone": "",
        "mobile": "",
        "email": "",
        "proof": {
          "description": "",
          "url": ""
        }
      },
      "current": {
        "home": "",
        "street": "",
        "city": "",
        "pinCode": "",
        "district": "",
        "state": "",
        "country": "",
        "phone": "",
        "mobile": "",
        "email": "",
        "proof": {
          "description": "",
          "url": ""
        }
      }
    }
  },
  "emergencyContacts": [
    {
      "name": "",
      "relation": "",
      "city": "",
      "contactNumber": "",
      "mobile": "",
      "email": ""
    }
  ],
  "professionalDetails": {
    "medicalRegistration": {
      "councilName": "",
      "registrationNumber": "",
      "validFrom": "",
      "validTo": "",
      "proof": {
        "description": "",
        "url": ""
      }
    },
    "specialties": [""],
    "qualifications": [
      {
        "degree": "",
        "specialization": "",
        "university": "",
        "institute": "",
        "yearOfCompletion": "",
        "duration": "",
        "mark": "",
        "documents": [""],
        "status": ""
      }
    ],
    "certifications": [
      {
        "name": "",
        "regNumber": "",
        "validFrom": "",
        "validTo": "",
        "dateOfUpdation": "",
        "status": ""
      }
    ],
    "experience": [
      {
        "hospitalName": "",
        "department": "",
        "designation": "",
        "from": "",
        "to": "",
        "salary": "",
        "documents": [""],
        "status": ""
      }
    ]
  },
  "family": [
    {
      "name": "",
      "relation": "",
      "dependent": "",
      "dateOfBirth": "",
      "aadharNumber": "",
      "occupation": "",
      "document": "",
      "status": ""
    }
  ],
  "languagesKnown": [
    {
      "language": "",
      "fluency": [""]
    }
  ],
  "bankDetails": [
    {
      "bankName": "",
      "branch": "",
      "ifscCode": "",
      "accountNumber": "",
      "document": "",
      "description": ""
    }
  ],
  "insurance": [
    {
      "policyName": "",
      "policyNumber": "",
      "validFrom": "",
      "validTo": "",
      "coverageAmount": "",
      "status": "",
      "documents": [""
      ]
    }
  ],
  "researchAndPublications": [
    {
      "title": "",
      "journal": "",
      "publicationDate": "",
      "link": "",
      "status": ""
    }
  ],
  "affiliations": [
    {
      "organizationName": "",
      "role": "",
      "from": "",
      "to": "",
      "status": ""
    }
  ],
  "documents": {
    "aadhar": {
      "number": "",
      "name": "",
      "issuedAt": "",
      "description": "",
      "url": ""
    },
    "passport": {
      "number": "",
      "name": "",
      "issuedAt": "",
      "renewedAt": "",
      "issuedPlace": "",
      "description": "",
      "url": ""
    },
    "panCard": {
      "number": "",
      "name": "",
      "issuedAt": "",
      "description": "",
      "url": ""
    },
    "medicalLicense": {
      "licenseNumber": "",
      "validFrom": "",
      "validTo": "",
      "description": "",
      "url": ""
    }
  }
}
  ;

const sensitiveFields = [
  "username",
  "general.fullName",
  "general.contactNumber",
  "general.workEmail",
  "personal.idProof.number",
  "personal.address.permanent.phone",
  "personal.address.permanent.email",
  "personal.address.current.phone",
  "personal.address.current.email",
  "emergencyContacts[].contactNumber",
  "emergencyContacts[].email"
];