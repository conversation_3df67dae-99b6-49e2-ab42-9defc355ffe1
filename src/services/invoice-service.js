const logging = require('../common/logging')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const paymentRepository = require('../repositories/payment-repository')
const patientRepository = require('../repositories/patient-repository')
const doctorService = require('./doctor-service')
const userService = require('./user-service')
const organizationRepository = require('../repositories/admin/organization-repository')
const { getDoctorByEncryptedEmailQuery } = require('../queries/doctor-query')
const {
  getPatientHistoryByPatientIdQuery,
  getPatientHistoryByPatientIdContainsQuery,
  getPatientConsultationsByPatientIdQuery,
  getLabTestDetailsQuery,
  getOrganizationTestDetailsQuery,
  getConsultationPaymentsByPatientQuery,
} = require('../queries/invoice-query')

/**
 * Extract plain text from HTML content
 * @param {string} html - HTML string
 * @returns {string[]} Array of text items extracted from HTML
 */
function extractTextFromHtml(html) {
  if (!html || typeof html !== 'string') return []

  // Remove HTML tags and get text content
  const textContent = html
    .replace(/<[^>]*>/g, '\n') // Replace tags with newlines
    .replace(/&nbsp;/g, ' ')   // Replace &nbsp; with space
    .replace(/&amp;/g, '&')    // Replace &amp; with &
    .replace(/&lt;/g, '<')     // Replace &lt; with <
    .replace(/&gt;/g, '>')     // Replace &gt; with >
    .replace(/&quot;/g, '"')   // Replace &quot; with "
    .split('\n')               // Split by newlines
    .map(line => line.trim())  // Trim each line
    .filter(line => line.length > 0) // Remove empty lines

  return textContent
}

/**
 * Invoice Service
 * Business logic for invoice management
 * Note: Invoices are essentially payments with enriched patient/doctor/bill details
 */
class InvoiceService {
  /**
   * Helper method: Get doctor by ID, falling back to userId lookup if needed
   * @param {string} doctorId - Can be actual doctorId or userId
   * @returns {object|null} Doctor object or null
   */
  async getDoctorByIdOrUserId(doctorId) {
    try {
      const { getDoctorByUserIdQuery } = require('../queries/doctor-query')
      let doctor = null

      doctor = await doctorService.getDoctor(doctorId)
      if (doctor) return doctor

      const userIdQuery = getDoctorByUserIdQuery(doctorId)
      doctor = await doctorService.queryDoctors(userIdQuery)
      if (doctor) return doctor

      const user = await userService.getUserById(doctorId)
      if (user && user.email) {
        const { encryptData } = require('../common/helper')
        const encryptedEmail = encryptData(user.email)
        const doctorQuery = getDoctorByEncryptedEmailQuery(encryptedEmail)
        doctor = await doctorService.queryDoctors(doctorQuery)
        if (doctor) return doctor
      }

      if (user) {
        doctor = {
          id: user.id,
          name: user.name || user.fullName,
          email: user.email,
          general: {
            fullName: user.name || user.fullName,
            designation: user.role || user.userRole,
            department: user.department || null,
            doctorID: user.employeeId || null,
          }
        }
      }

      return doctor
    } catch (error) {
      logging.logError(`Error fetching doctor with ID ${doctorId}:`, error)
      return null
    }
  }
  /**
   * Get all invoices for an organization with filters, search, and pagination
   */
  async getInvoicesByOrganization({
    organizationId,
    page = 1,
    limit = 20,
    searchTerm,
    paymentType,
    startDate,
    endDate,
    gender,
  }) {
    try {
      
      const filters = {}
      if (paymentType) filters.paymentType = paymentType
      if (startDate) filters.startDate = startDate
      if (endDate) filters.endDate = endDate

      const result = await paymentRepository.getPaymentsByOrganization(
        organizationId,
        filters,
        null,
        1000, // Fetch all, we'll paginate in memory
      )

      const allPayments = result.items || result.resources || []
      // Filter out subscription payments from invoice listing
      const payments = allPayments.filter(p => p.paymentType !== 'subscription')

      const enrichedInvoices = await this.enrichInvoices(payments)

      let filteredInvoices = enrichedInvoices
      if (searchTerm && searchTerm.trim() !== '') {
        filteredInvoices = this.searchInvoices(enrichedInvoices, searchTerm)
      }
     
      if (gender) {
        filteredInvoices = filteredInvoices.filter(
          (invoice) => invoice.gender === gender,
        )
      }

      filteredInvoices.sort((a, b) => {
        const dateA = new Date(a.rawPayment.createdAt || 0)
        const dateB = new Date(b.rawPayment.createdAt || 0)
        return dateB - dateA
      })

      
      const paginatedResult = this.paginateInvoices(
        filteredInvoices,
        page,
        limit,
      )

      return {
        success: true,
        data: paginatedResult,
      }
    } catch (error) {
      logging.logError('Error in getInvoicesByOrganization:', error)
      throw new Error(
        `Failed to fetch invoices: ${error.message || 'Unknown error'}`,
      )
    }
  }

  /**
   * Get invoice by ID with full details
   */
  async getInvoiceById(invoiceId) {
    try {
      const payment = await paymentRepository.getPaymentById(invoiceId)

      if (!payment) {
        return {
          success: false,
          message: 'Invoice not found',
        }
      }

      let patientDetails = null
      if (payment.patientId) {
        try {
          patientDetails = await patientRepository.getPatientById(payment.patientId)

          if (!patientDetails && payment.organizationId) {
            patientDetails = await patientRepository.getPatientByRegistrationNumber(
              payment.patientId,
              payment.organizationId
            )
          }
        } catch (error) {
          logging.logError('Error fetching patient for invoice:', error)
        }
      }

      let doctorDetails = null
      const doctorId = payment.metadata?.doctorId || payment.notes?.doctorId || payment.created_by
      if (doctorId) {
        doctorDetails = await this.getDoctorByIdOrUserId(doctorId)
      }

      let billDetails = null
      try {
        switch (payment.paymentType) {
          case 'consultation':
            billDetails = await this.getConsultationBillDetails(payment)
            break
          case 'pharmacy':
          case 'prescription':
            billDetails = await this.getPrescriptionBillDetails(payment)
            break
          case 'lab_test':
            billDetails = await this.getLabTestBillDetails(payment)
            break
          case 'subscription':
            billDetails = await this.getSubscriptionBillDetails(payment)
            break
          default:
            billDetails = null
        }
      } catch (error) {
        logging.logError('Error fetching bill details:', error)
      }

      let doctorSummary = null
      if (doctorDetails) {
        doctorSummary = {
          id: doctorDetails.id,
          name: doctorDetails.name || doctorDetails.general?.fullName || doctorDetails.data?.name || 'N/A',
          email: doctorDetails.username || doctorDetails.email,
          designation: doctorDetails.general?.designation,
          department: doctorDetails.general?.department,
        }
      }

      const invoice = {
        ...payment,
        amountInRupees: payment.amount / 100,
        patient: patientDetails,
        doctor: doctorSummary,
        billDetails,
      }

      return {
        success: true,
        data: invoice,
      }
    } catch (error) {
      logging.logError('Error in getInvoiceById:', error)
      throw new Error(
        `Failed to fetch invoice: ${error.message || 'Unknown error'}`,
      )
    }
  }

  /**
   * Enrich multiple invoices with patient and doctor details
   */
  async enrichInvoices(payments) {
    return Promise.all(payments.map((payment) => this.enrichInvoice(payment)))
  }

  /**
   * Enrich a single invoice with patient and doctor details
   */
  async enrichInvoice(payment) {
    let patientName = 'N/A'
    let patientUUID = null 
    let actualPatientId = payment.patientId || payment.notes?.patientId || payment.metadata?.patientId || null
    let patientGender = 'N/A'

    if (actualPatientId) {
      try {
        let patient = await patientRepository.getPatientById(actualPatientId)

        if (!patient && payment.organizationId) {
          patient = await patientRepository.getPatientByRegistrationNumber(actualPatientId, payment.organizationId)
        }

        if (patient) {
          patientUUID = patient.id
          patientName = patient.name || 'N/A'
          patientGender = patient.gender || patient.sex || 'N/A'
        }
      } catch (error) {
        logging.logError(`Error fetching patient for invoice (patientId: ${actualPatientId}):`, error)
      }
    }

    let doctorName = 'N/A'

    const doctorId = payment.metadata?.doctorId || payment.notes?.doctorId || payment.created_by
    if (doctorId) {
      const doctor = await this.getDoctorByIdOrUserId(doctorId)
      if (doctor) {
        doctorName = doctor.name || doctor.general?.fullName || doctor.data?.name || 'N/A'
      } else if (payment.created_by_name) {
        doctorName = payment.created_by_name
      }
    } else if (payment.created_by_name) {
      doctorName = payment.created_by_name
    }

    return {
      date: payment.createdAt
        ? new Date(payment.createdAt).toLocaleDateString('en-GB')
        : 'N/A',
      type: payment.paymentType || 'N/A',
      patientId: patientUUID || actualPatientId || '-', 
      fullName: patientName,
      gender: patientGender,
      amount: payment.amount / 100, // Convert from paise to rupees
      modeOfPayment: payment.notes?.paymentMethod || 'Card',
      transactionId: payment.razorpayPaymentId || payment.razorpayOrderId || '-',
      doctorName: doctorName,
      paymentId: payment.id,
      status: payment.status,
      rawPayment: payment,
    }
  }

  /**
   * Search invoices by multiple fields
   */
  searchInvoices(invoices, searchTerm) {
    const searchLower = searchTerm.toLowerCase()
    return invoices.filter(
      (invoice) =>
        invoice.fullName.toLowerCase().startsWith(searchLower) ||
        invoice.patientId.toLowerCase().startsWith(searchLower),
    )
  }

  /**
   * Paginate invoices
   */
  paginateInvoices(invoices, page, limit) {
    const totalCount = invoices.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedInvoices = invoices.slice(startIndex, endIndex)

    return {
      invoices: paginatedInvoices,
      total: totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
    }
  }

  /**
   * Get consultation bill details
   */
  async getConsultationBillDetails(payment) {
    try {
      const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
      const patientHistoryContainer = 'PatientHistory'

      let consultationDetails = null

      if (payment.patientId) {
        const paymentDate = new Date(payment.createdAt || payment.created_on)

        let historyRecords = []

        const query1 = getPatientHistoryByPatientIdQuery(payment.patientId)
        historyRecords = await cosmosDbContext.queryItems(query1, patientHistoryContainer)

        if ((!historyRecords || historyRecords.length === 0) && payment.organizationId) {
          const query2 = getPatientHistoryByPatientIdContainsQuery(payment.patientId)
          historyRecords = await cosmosDbContext.queryItems(query2, patientHistoryContainer)
        }

        if (historyRecords && historyRecords.length > 0) {
          const sortedRecords = historyRecords
            .map(record => ({
              ...record,
              timeDiff: Math.abs(new Date(record.updated_on || record.created_on) - paymentDate)
            }))
            .sort((a, b) => a.timeDiff - b.timeDiff)

          if (sortedRecords.length > 0 && sortedRecords[0].timeDiff < 24 * 60 * 60 * 1000) {
            consultationDetails = sortedRecords[0]
          } else if (sortedRecords.length > 0) {
            consultationDetails = sortedRecords[0]
          }
        }

        if (!consultationDetails) {
          const patientConsultationsContainer = 'PatientConsultations'
          const consultQuery = getPatientConsultationsByPatientIdQuery(payment.patientId)
          const consultRecords = await cosmosDbContext.queryItems(consultQuery, patientConsultationsContainer)

          if (consultRecords && consultRecords.length > 0) {
            const sortedConsults = consultRecords
              .map(record => ({
                ...record,
                timeDiff: Math.abs(new Date(record.updated_on || record.created_on) - paymentDate)
              }))
              .sort((a, b) => a.timeDiff - b.timeDiff)

            if (sortedConsults.length > 0) {
              consultationDetails = sortedConsults[0]
            }
          }
        }
      }

      const response = {
        type: 'consultation',
        consultationFee: payment.amount / 100,
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedBy: payment.metadata?.billedBy || payment.created_by_name,
        billedAt: payment.createdAt,
      }

      if (consultationDetails) {
        response.consultationId = consultationDetails.id
        response.consultationDate = consultationDetails.updated_on || consultationDetails.created_on
        response.status = consultationDetails.status

        if (consultationDetails.summary) {
          const summary = consultationDetails.summary
          response.summary = {
            presentingComplaints: extractTextFromHtml(summary.presentingcomplaints),
            historyOfPresenting: extractTextFromHtml(summary.historyofpresenting),
            pastMedicalHistory: extractTextFromHtml(summary.pastmedicalhistory),
            vitals: summary.vitals || null,
            anthropometry: summary.anthropometry || null,
            generalPhysicalExamination: summary.generalphysicalexamination || null,
            systemicExamination: summary.systemicexamination || null,
            heent: summary.heent ? extractTextFromHtml(summary.heent) : null,
            conversation: summary.conversation || [],
            owner: summary.owner || null,
          }
        }

        if (consultationDetails.doctorId || consultationDetails.created_by) {
          response.doctorId = consultationDetails.doctorId || consultationDetails.created_by
        }
      }

      return response
    } catch (error) {
      logging.logError('Error getting consultation bill details:', error)
      return {
        type: 'consultation',
        consultationFee: payment.amount / 100,
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedBy: payment.metadata?.billedBy || payment.created_by_name,
        billedAt: payment.createdAt,
      }
    }
  }

  /**
   * Get prescription (pharmacy) bill details
   */
  async getPrescriptionBillDetails(payment) {
    try {
      const prescriptionRepository = require('../repositories/prescription-repository')

      let prescriptionId =
        payment.metadata?.prescriptionId || payment.notes?.prescriptionId

      let prescription = null

      if (prescriptionId) {
        const result = await prescriptionRepository.getPrescriptionById(prescriptionId)
        prescription = Array.isArray(result) ? result[0] : result
      }

      if (!prescription && payment.patientId) {
        const prescriptions = await prescriptionRepository.getPrescriptionsByPatient(
          payment.patientId
        )

        if (prescriptions && prescriptions.length > 0) {
          const paymentDate = new Date(payment.createdAt || payment.created_on)

          const sortedPrescriptions = prescriptions
            .filter(p => p.medicines && p.medicines.length > 0)
            .map(p => ({
              ...p,
              timeDiff: Math.abs(new Date(p.updated_on || p.created_on) - paymentDate)
            }))
            .sort((a, b) => a.timeDiff - b.timeDiff)

          if (sortedPrescriptions.length > 0 && sortedPrescriptions[0].timeDiff < 24 * 60 * 60 * 1000) {
            prescription = sortedPrescriptions[0]
          } else if (sortedPrescriptions.length > 0) {
            prescription = sortedPrescriptions[0]
          }
        }
      }

      if (!prescription || !prescription.medicines) {
        return {
          type: 'prescription',
          items: [],
          totalAmount: payment.amount / 100,
          paymentMethod: payment.notes?.paymentMethod || 'Card',
          billedBy: payment.metadata?.billedBy || payment.created_by_name,
          billedAt: payment.createdAt,
        }
      }

      const items = prescription.medicines.map((med, index) => ({
        no: index + 1,
        drugName: med.drugForm || med.drugName || med.name || 'N/A',
        genericName: med.genericName || med.name || 'N/A',
        brandName: med.brandName || 'N/A',
        strength: med.strength || 'N/A',
        qty: med.quantity || med.qty || 1,
        cost: med.cost || med.price || 0,
      }))

      return {
        type: 'prescription',
        prescriptionId: prescription.id,
        doctorName: prescription.doctor || 'N/A',
        doctorEmail: prescription.doctorEmail || 'N/A',
        prescriptionDate: prescription.created_on || prescription.updated_on,
        items,
        totalAmount: payment.amount / 100,
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedBy: payment.metadata?.billedBy || payment.created_by_name,
        billedAt: payment.createdAt,
      }
    } catch (error) {
      logging.logError('Error getting prescription bill details:', error)
      return {
        type: 'prescription',
        items: [],
        totalAmount: payment.amount / 100,
      }
    }
  }

  /**
   * Get lab test bill details
   */
  async getLabTestBillDetails(payment) {
    try {
      const patientLabTestRepository = require('../repositories/patient-lab-test-repository')
      const cosmosDbContext = require('../cosmosDbContext/comosdb-context')

      let labTestId = payment.metadata?.labTestId || payment.notes?.labTestId

      let labTestRecord = null

      if (labTestId) {
        const result = await patientLabTestRepository.getLabTestById(labTestId)
        labTestRecord = Array.isArray(result) ? result[0] : result
      }

      if (!labTestRecord && payment.patientId) {
        const labTests = await patientLabTestRepository.getLabTestsByPatientId(
          payment.patientId
        )

        if (labTests && labTests.length > 0) {
          const paymentDate = new Date(payment.createdAt || payment.created_on)

          const sortedLabTests = labTests
            .filter(lt => lt.labTests && (Array.isArray(lt.labTests) ? lt.labTests.length > 0 : Object.keys(lt.labTests).length > 0))
            .map(lt => ({
              ...lt,
              timeDiff: Math.abs(new Date(lt.updated_on || lt.created_on) - paymentDate)
            }))
            .sort((a, b) => a.timeDiff - b.timeDiff)

          if (sortedLabTests.length > 0) {
            labTestRecord = sortedLabTests[0]
          }
        }
      }

      if (!labTestRecord || !labTestRecord.labTests) {
        return {
          type: 'lab_test',
          tests: [],
          totalAmount: payment.amount / 100,
          paymentMethod: payment.notes?.paymentMethod || 'Card',
          billedBy: payment.metadata?.billedBy || payment.created_by_name,
          billedAt: payment.createdAt,
        }
      }

      const getTestDetails = async (testId, organizationId) => {
        try {
          const standardQuery = getLabTestDetailsQuery(testId)
          const standardResult = await cosmosDbContext.queryItems(standardQuery, 'lab_tests')

          if (standardResult && standardResult.length > 0) {
            return standardResult[0]
          }

          if (organizationId) {
            const orgQuery = getOrganizationTestDetailsQuery(testId, organizationId)
            const orgResult = await cosmosDbContext.queryItems(orgQuery, 'OrganizationTests')

            if (orgResult && orgResult.length > 0) {
              return orgResult[0]
            }
          }

          return null
        } catch (error) {
          logging.logError(`Error fetching test details for ${testId}:`, error)
          return null
        }
      }

      const { getDepartmentByClassCode } = require('../common/class-department-mapping')

      let tests = []
      const labTests = labTestRecord.labTests

      if (Array.isArray(labTests)) {
        for (const test of labTests) {
          const testDetails = await getTestDetails(test.testId, payment.organizationId)
          const department = testDetails?.CLASS ? getDepartmentByClassCode(testDetails.CLASS) : (test.department || 'N/A')

          tests.push({
            department: department,
            testName: test.testName || testDetails?.DisplayName || testDetails?.LONG_COMMON_NAME || 'N/A',
            subTests: test.subTests || [],
            amount: test.price || testDetails?.price || test.amount || 0,
          })
        }
      } else if (typeof labTests === 'object') {
        for (const [department, departmentTests] of Object.entries(labTests)) {
          if (Array.isArray(departmentTests)) {
            for (const test of departmentTests) {
              const testDetails = await getTestDetails(test.testId, payment.organizationId)

              tests.push({
                department: department || 'N/A',
                testName: test.testName || testDetails?.DisplayName || testDetails?.LONG_COMMON_NAME || 'N/A',
                subTests: test.subTests || [],
                amount: test.price || testDetails?.price || test.amount || 0,
              })
            }
          }
        }
      }

      return {
        type: 'lab_test',
        labTestId: labTestRecord.id,
        tests,
        totalAmount: payment.amount / 100,
        labTechnician:
          labTestRecord.labTechnician || payment.metadata?.labTechnician || 'N/A',
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedBy: payment.metadata?.billedBy || payment.created_by_name,
        billedAt: payment.createdAt,
        testDate: labTestRecord.created_on || labTestRecord.updated_on,
      }
    } catch (error) {
      logging.logError('Error getting lab test bill details:', error)
      return {
        type: 'lab_test',
        tests: [],
        totalAmount: payment.amount / 100,
      }
    }
  }

  /**
   * Get subscription bill details
   */
  async getSubscriptionBillDetails(payment) {
    try {
      return {
        type: 'subscription',
        subscriberEmail: payment.subscriberEmail || 'N/A',
        subscriptionAmount: payment.amount / 100,
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedAt: payment.createdAt,
        description: payment.description || 'Subscription Payment',
      }
    } catch (error) {
      logging.logError('Error getting subscription bill details:', error)
      return {
        type: 'subscription',
        totalAmount: payment.amount / 100,
      }
    }
  }

  /**
   * Get patient profile with previous consultations (for Super Admin)
   */
  async getPatientProfileWithConsultations(patientId, organizationId) {
    try {
      const patientService = require('./patient-service')

      const patient = await patientService.GetPatientProfile(patientId, [])

      let organizationName = 'N/A'
      const orgId = patient?.organizationId || organizationId
      if (orgId) {
        try {
          const organization = await organizationRepository.getOrganizationById(orgId)
          if (organization) {
            organizationName = organization.name || organization.organizationName || 'N/A'
          }
        } catch (orgError) {
          logging.logError(`Error fetching organization ${orgId}:`, orgError)
        }
      }

      if (!patient) {
        throw new Error('Patient not found')
      }

      let consultationRecords = []
      try {
        const queueQuery = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND (STARTSWITH(c.status, 'Done') OR c.status = 'Consultation-Done') ORDER BY c.updated_on DESC`
        consultationRecords = await cosmosDbContext.queryItems(queueQuery, 'Queues') || []
      } catch (queryError) {
        logging.logError(`Error fetching consultations for ${patientId}:`, queryError)
      }

      const doctorCache = {}

      const enrichedConsultations = []
      if (consultationRecords && consultationRecords.length > 0) {
        for (const record of consultationRecords) {
          const doctorId = record.doctorId || record.created_by
          let department = 'N/A'
          let consultationFee = 0

          let employeeId = ''
          if (doctorId) {
            try {
              if (!doctorCache[doctorId]) {
                const doctor = await this.getDoctorByIdOrUserId(doctorId)
                doctorCache[doctorId] = doctor
              }
              const doctor = doctorCache[doctorId]
              if (doctor) {
                const deptValue = doctor.general?.department
                department = (deptValue && deptValue.trim() !== '') ? deptValue : 'N/A'
                consultationFee = doctor.consultationFee || 0
                const generalDoctorId = doctor.general?.doctorID
                employeeId = (generalDoctorId && generalDoctorId.trim() !== '') ? generalDoctorId : ''
              }
            } catch (doctorError) {
              logging.logError(`Error fetching doctor ${doctorId}:`, doctorError)
            }
          }

          enrichedConsultations.push({
            date: record.consultationEndTime || record.consultationStartTime || record.updated_on || record.created_on,
            doctorId: doctorId,
            employeeId: employeeId,
            department: department,
            consultationFee: consultationFee,
          })
        }
      }

      let age = patient.age || null
      if (!age && patient.dob) {
        const dob = new Date(patient.dob)
        const today = new Date()
        age = today.getFullYear() - dob.getFullYear()
        const monthDiff = today.getMonth() - dob.getMonth()
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
          age--
        }
      }

      return {
        patient: {
          id: patient.id,
          name: patient.name || 'N/A',
          age: age,
          dateOfBirth: patient.dob || 'N/A',
          abhaNumber: patient.abhaNumber || patient.ABHA || 'N/A',
          organization: organizationName,
          organizationId: patient.organizationId,
          registrationDate: patient.created_on || 'N/A',
          gender: patient.gender || patient.sex || 'N/A',
          phone: patient.phone || 'N/A',
          email: patient.email || 'N/A',
        },
        previousConsultations: enrichedConsultations,
      }
    } catch (error) {
      logging.logError(`Error fetching patient profile with consultations for ${patientId}:`, error)
      throw error
    }
  }
}

module.exports = new InvoiceService()
